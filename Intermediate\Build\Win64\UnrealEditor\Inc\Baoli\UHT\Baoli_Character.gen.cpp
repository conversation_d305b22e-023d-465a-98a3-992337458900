// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Baoli/Player/Baoli_Character.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeBaoli_Character() {}

// Begin Cross Module References
BAOLI_API UClass* Z_Construct_UClass_ABaoli_Character();
BAOLI_API UClass* Z_Construct_UClass_ABaoli_Character_NoRegister();
BAOLI_API UClass* Z_Construct_UClass_ABaoli_Controller_NoRegister();
BAOLI_API UEnum* Z_Construct_UEnum_Baoli_ECoverSystem();
BAOLI_API UEnum* Z_Construct_UEnum_Baoli_EMyGait();
ENGINE_API UClass* Z_Construct_UClass_ACharacter();
ENGINE_API UClass* Z_Construct_UClass_UCameraComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UCharacterMovementComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UCurveFloat_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UPointLightComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USpringArmComponent_NoRegister();
ENHANCEDINPUT_API UClass* Z_Construct_UClass_UInputAction_NoRegister();
ENHANCEDINPUT_API UClass* Z_Construct_UClass_UInputMappingContext_NoRegister();
MOTIONWARPING_API UClass* Z_Construct_UClass_UMotionWarpingComponent_NoRegister();
UPackage* Z_Construct_UPackage__Script_Baoli();
// End Cross Module References

// Begin Enum EMyGait
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EMyGait;
static UEnum* EMyGait_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EMyGait.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EMyGait.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Baoli_EMyGait, (UObject*)Z_Construct_UPackage__Script_Baoli(), TEXT("EMyGait"));
	}
	return Z_Registration_Info_UEnum_EMyGait.OuterSingleton;
}
template<> BAOLI_API UEnum* StaticEnum<EMyGait>()
{
	return EMyGait_StaticEnum();
}
struct Z_Construct_UEnum_Baoli_EMyGait_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//Gait Reference\n" },
#endif
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
		{ "Run.DisplayName", "Run" },
		{ "Run.Name", "Run" },
		{ "Sprint.DisplayName", "Sprint" },
		{ "Sprint.Name", "Sprint" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gait Reference" },
#endif
		{ "Walk.DisplayName", "Walk" },
		{ "Walk.Name", "Walk" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "Walk", (int64)Walk },
		{ "Run", (int64)Run },
		{ "Sprint", (int64)Sprint },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Baoli_EMyGait_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Baoli,
	nullptr,
	"EMyGait",
	"EMyGait",
	Z_Construct_UEnum_Baoli_EMyGait_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Baoli_EMyGait_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::Regular,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Baoli_EMyGait_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Baoli_EMyGait_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Baoli_EMyGait()
{
	if (!Z_Registration_Info_UEnum_EMyGait.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EMyGait.InnerSingleton, Z_Construct_UEnum_Baoli_EMyGait_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EMyGait.InnerSingleton;
}
// End Enum EMyGait

// Begin Enum ECoverSystem
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ECoverSystem;
static UEnum* ECoverSystem_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ECoverSystem.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ECoverSystem.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_Baoli_ECoverSystem, (UObject*)Z_Construct_UPackage__Script_Baoli(), TEXT("ECoverSystem"));
	}
	return Z_Registration_Info_UEnum_ECoverSystem.OuterSingleton;
}
template<> BAOLI_API UEnum* StaticEnum<ECoverSystem>()
{
	return ECoverSystem_StaticEnum();
}
struct Z_Construct_UEnum_Baoli_ECoverSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Almari.DisplayName", "Almari" },
		{ "Almari.Name", "Almari" },
		{ "Bed.DisplayName", "Bed" },
		{ "Bed.Name", "Bed" },
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//Cover Reference\n" },
#endif
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "None" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cover Reference" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "None", (int64)None },
		{ "Almari", (int64)Almari },
		{ "Bed", (int64)Bed },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_Baoli_ECoverSystem_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_Baoli,
	nullptr,
	"ECoverSystem",
	"ECoverSystem",
	Z_Construct_UEnum_Baoli_ECoverSystem_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_Baoli_ECoverSystem_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::Regular,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_Baoli_ECoverSystem_Statics::Enum_MetaDataParams), Z_Construct_UEnum_Baoli_ECoverSystem_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_Baoli_ECoverSystem()
{
	if (!Z_Registration_Info_UEnum_ECoverSystem.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ECoverSystem.InnerSingleton, Z_Construct_UEnum_Baoli_ECoverSystem_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ECoverSystem.InnerSingleton;
}
// End Enum ECoverSystem

// Begin Class ABaoli_Character Function DisableCharacter
struct Z_Construct_UFunction_ABaoli_Character_DisableCharacter_Statics
{
	struct Baoli_Character_eventDisableCharacter_Parms
	{
		bool bDisableInput;
		bool bDisableMesh;
		bool bDisableCursor;
		bool bAnimationCamera;
		bool bDisableSkeletonUpdate;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bDisableInput_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bDisableInput;
	static void NewProp_bDisableMesh_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bDisableMesh;
	static void NewProp_bDisableCursor_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bDisableCursor;
	static void NewProp_bAnimationCamera_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAnimationCamera;
	static void NewProp_bDisableSkeletonUpdate_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bDisableSkeletonUpdate;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ABaoli_Character_DisableCharacter_Statics::NewProp_bDisableInput_SetBit(void* Obj)
{
	((Baoli_Character_eventDisableCharacter_Parms*)Obj)->bDisableInput = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ABaoli_Character_DisableCharacter_Statics::NewProp_bDisableInput = { "bDisableInput", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(Baoli_Character_eventDisableCharacter_Parms), &Z_Construct_UFunction_ABaoli_Character_DisableCharacter_Statics::NewProp_bDisableInput_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ABaoli_Character_DisableCharacter_Statics::NewProp_bDisableMesh_SetBit(void* Obj)
{
	((Baoli_Character_eventDisableCharacter_Parms*)Obj)->bDisableMesh = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ABaoli_Character_DisableCharacter_Statics::NewProp_bDisableMesh = { "bDisableMesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(Baoli_Character_eventDisableCharacter_Parms), &Z_Construct_UFunction_ABaoli_Character_DisableCharacter_Statics::NewProp_bDisableMesh_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ABaoli_Character_DisableCharacter_Statics::NewProp_bDisableCursor_SetBit(void* Obj)
{
	((Baoli_Character_eventDisableCharacter_Parms*)Obj)->bDisableCursor = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ABaoli_Character_DisableCharacter_Statics::NewProp_bDisableCursor = { "bDisableCursor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(Baoli_Character_eventDisableCharacter_Parms), &Z_Construct_UFunction_ABaoli_Character_DisableCharacter_Statics::NewProp_bDisableCursor_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ABaoli_Character_DisableCharacter_Statics::NewProp_bAnimationCamera_SetBit(void* Obj)
{
	((Baoli_Character_eventDisableCharacter_Parms*)Obj)->bAnimationCamera = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ABaoli_Character_DisableCharacter_Statics::NewProp_bAnimationCamera = { "bAnimationCamera", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(Baoli_Character_eventDisableCharacter_Parms), &Z_Construct_UFunction_ABaoli_Character_DisableCharacter_Statics::NewProp_bAnimationCamera_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ABaoli_Character_DisableCharacter_Statics::NewProp_bDisableSkeletonUpdate_SetBit(void* Obj)
{
	((Baoli_Character_eventDisableCharacter_Parms*)Obj)->bDisableSkeletonUpdate = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ABaoli_Character_DisableCharacter_Statics::NewProp_bDisableSkeletonUpdate = { "bDisableSkeletonUpdate", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(Baoli_Character_eventDisableCharacter_Parms), &Z_Construct_UFunction_ABaoli_Character_DisableCharacter_Statics::NewProp_bDisableSkeletonUpdate_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ABaoli_Character_DisableCharacter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaoli_Character_DisableCharacter_Statics::NewProp_bDisableInput,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaoli_Character_DisableCharacter_Statics::NewProp_bDisableMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaoli_Character_DisableCharacter_Statics::NewProp_bDisableCursor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaoli_Character_DisableCharacter_Statics::NewProp_bAnimationCamera,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaoli_Character_DisableCharacter_Statics::NewProp_bDisableSkeletonUpdate,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaoli_Character_DisableCharacter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaoli_Character_DisableCharacter_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ABaoli_Character, nullptr, "DisableCharacter", nullptr, nullptr, Z_Construct_UFunction_ABaoli_Character_DisableCharacter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ABaoli_Character_DisableCharacter_Statics::PropPointers), sizeof(Z_Construct_UFunction_ABaoli_Character_DisableCharacter_Statics::Baoli_Character_eventDisableCharacter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaoli_Character_DisableCharacter_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaoli_Character_DisableCharacter_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ABaoli_Character_DisableCharacter_Statics::Baoli_Character_eventDisableCharacter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ABaoli_Character_DisableCharacter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaoli_Character_DisableCharacter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaoli_Character::execDisableCharacter)
{
	P_GET_UBOOL(Z_Param_bDisableInput);
	P_GET_UBOOL(Z_Param_bDisableMesh);
	P_GET_UBOOL(Z_Param_bDisableCursor);
	P_GET_UBOOL(Z_Param_bAnimationCamera);
	P_GET_UBOOL(Z_Param_bDisableSkeletonUpdate);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DisableCharacter(Z_Param_bDisableInput,Z_Param_bDisableMesh,Z_Param_bDisableCursor,Z_Param_bAnimationCamera,Z_Param_bDisableSkeletonUpdate);
	P_NATIVE_END;
}
// End Class ABaoli_Character Function DisableCharacter

// Begin Class ABaoli_Character Function EnableCharacter
struct Z_Construct_UFunction_ABaoli_Character_EnableCharacter_Statics
{
	struct Baoli_Character_eventEnableCharacter_Parms
	{
		bool bEnableInput;
		bool bEnableMesh;
		bool bEnableCursor;
		bool bPlayerCamera;
		bool bEnableSkeletonUpdate;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableInput_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableInput;
	static void NewProp_bEnableMesh_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableMesh;
	static void NewProp_bEnableCursor_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableCursor;
	static void NewProp_bPlayerCamera_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPlayerCamera;
	static void NewProp_bEnableSkeletonUpdate_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableSkeletonUpdate;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ABaoli_Character_EnableCharacter_Statics::NewProp_bEnableInput_SetBit(void* Obj)
{
	((Baoli_Character_eventEnableCharacter_Parms*)Obj)->bEnableInput = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ABaoli_Character_EnableCharacter_Statics::NewProp_bEnableInput = { "bEnableInput", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(Baoli_Character_eventEnableCharacter_Parms), &Z_Construct_UFunction_ABaoli_Character_EnableCharacter_Statics::NewProp_bEnableInput_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ABaoli_Character_EnableCharacter_Statics::NewProp_bEnableMesh_SetBit(void* Obj)
{
	((Baoli_Character_eventEnableCharacter_Parms*)Obj)->bEnableMesh = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ABaoli_Character_EnableCharacter_Statics::NewProp_bEnableMesh = { "bEnableMesh", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(Baoli_Character_eventEnableCharacter_Parms), &Z_Construct_UFunction_ABaoli_Character_EnableCharacter_Statics::NewProp_bEnableMesh_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ABaoli_Character_EnableCharacter_Statics::NewProp_bEnableCursor_SetBit(void* Obj)
{
	((Baoli_Character_eventEnableCharacter_Parms*)Obj)->bEnableCursor = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ABaoli_Character_EnableCharacter_Statics::NewProp_bEnableCursor = { "bEnableCursor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(Baoli_Character_eventEnableCharacter_Parms), &Z_Construct_UFunction_ABaoli_Character_EnableCharacter_Statics::NewProp_bEnableCursor_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ABaoli_Character_EnableCharacter_Statics::NewProp_bPlayerCamera_SetBit(void* Obj)
{
	((Baoli_Character_eventEnableCharacter_Parms*)Obj)->bPlayerCamera = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ABaoli_Character_EnableCharacter_Statics::NewProp_bPlayerCamera = { "bPlayerCamera", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(Baoli_Character_eventEnableCharacter_Parms), &Z_Construct_UFunction_ABaoli_Character_EnableCharacter_Statics::NewProp_bPlayerCamera_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ABaoli_Character_EnableCharacter_Statics::NewProp_bEnableSkeletonUpdate_SetBit(void* Obj)
{
	((Baoli_Character_eventEnableCharacter_Parms*)Obj)->bEnableSkeletonUpdate = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ABaoli_Character_EnableCharacter_Statics::NewProp_bEnableSkeletonUpdate = { "bEnableSkeletonUpdate", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(Baoli_Character_eventEnableCharacter_Parms), &Z_Construct_UFunction_ABaoli_Character_EnableCharacter_Statics::NewProp_bEnableSkeletonUpdate_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ABaoli_Character_EnableCharacter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaoli_Character_EnableCharacter_Statics::NewProp_bEnableInput,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaoli_Character_EnableCharacter_Statics::NewProp_bEnableMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaoli_Character_EnableCharacter_Statics::NewProp_bEnableCursor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaoli_Character_EnableCharacter_Statics::NewProp_bPlayerCamera,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ABaoli_Character_EnableCharacter_Statics::NewProp_bEnableSkeletonUpdate,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaoli_Character_EnableCharacter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ABaoli_Character_EnableCharacter_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ABaoli_Character, nullptr, "EnableCharacter", nullptr, nullptr, Z_Construct_UFunction_ABaoli_Character_EnableCharacter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ABaoli_Character_EnableCharacter_Statics::PropPointers), sizeof(Z_Construct_UFunction_ABaoli_Character_EnableCharacter_Statics::Baoli_Character_eventEnableCharacter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ABaoli_Character_EnableCharacter_Statics::Function_MetaDataParams), Z_Construct_UFunction_ABaoli_Character_EnableCharacter_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ABaoli_Character_EnableCharacter_Statics::Baoli_Character_eventEnableCharacter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ABaoli_Character_EnableCharacter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ABaoli_Character_EnableCharacter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ABaoli_Character::execEnableCharacter)
{
	P_GET_UBOOL(Z_Param_bEnableInput);
	P_GET_UBOOL(Z_Param_bEnableMesh);
	P_GET_UBOOL(Z_Param_bEnableCursor);
	P_GET_UBOOL(Z_Param_bPlayerCamera);
	P_GET_UBOOL(Z_Param_bEnableSkeletonUpdate);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableCharacter(Z_Param_bEnableInput,Z_Param_bEnableMesh,Z_Param_bEnableCursor,Z_Param_bPlayerCamera,Z_Param_bEnableSkeletonUpdate);
	P_NATIVE_END;
}
// End Class ABaoli_Character Function EnableCharacter

// Begin Class ABaoli_Character
void ABaoli_Character::StaticRegisterNativesABaoli_Character()
{
	UClass* Class = ABaoli_Character::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "DisableCharacter", &ABaoli_Character::execDisableCharacter },
		{ "EnableCharacter", &ABaoli_Character::execEnableCharacter },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(ABaoli_Character);
UClass* Z_Construct_UClass_ABaoli_Character_NoRegister()
{
	return ABaoli_Character::StaticClass();
}
struct Z_Construct_UClass_ABaoli_Character_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "HideCategories", "Navigation" },
		{ "IncludePath", "Player/Baoli_Character.h" },
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpringArmComponent_MetaData[] = {
		{ "Category", "Baoli_Character" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//Spring Arm\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spring Arm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CameraComponent_MetaData[] = {
		{ "Category", "Baoli_Character" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//Camera\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Camera" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FOV_MetaData[] = {
		{ "Category", "Camera" },
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CameraLagSpeedStart_MetaData[] = {
		{ "Category", "Camera" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Camera Rotation Lag Settings\n" },
#endif
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Camera Rotation Lag Settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CameraLagSpeedEnd_MetaData[] = {
		{ "Category", "Camera" },
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CameraLagTransitionDuration_MetaData[] = {
		{ "Category", "Camera" },
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NoCameraRotationLag_MetaData[] = {
		{ "Category", "Camera" },
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InspectionLightComponent_MetaData[] = {
		{ "Category", "Baoli_Character" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//Inspection Light\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inspection Light" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InspectionLightIntensity_MetaData[] = {
		{ "Category", "InspectionLight" },
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InspectionAttenuationRadius_MetaData[] = {
		{ "Category", "InpsectionLight" },
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MotionWarpingComponent_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerController_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "//Player Controller Reference\n" },
#endif
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Player Controller Reference" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InputMapping_MetaData[] = {
		{ "Category", "Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//Input mapping Context\n" },
#endif
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Input mapping Context" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LookAction_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Input" },
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WalkAction_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Input" },
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SprintAction_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Input" },
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StrafeAction_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Input" },
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AimAction_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Input" },
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CrouchAction_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Input" },
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_JumpAction_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Input" },
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bWantsToSprint_MetaData[] = {
		{ "Category", "Movement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//Movement Variables\n" },
#endif
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Movement Variables" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentGait_MetaData[] = {
		{ "Category", "Movement" },
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CoverSystem_MetaData[] = {
		{ "Category", "Variables" },
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementComponent_MetaData[] = {
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpeedMapCurve_MetaData[] = {
		{ "Category", "Movement" },
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bWantsToStrafe_MetaData[] = {
		{ "Category", "Movement" },
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AimSensitivity_MetaData[] = {
		{ "Category", "Sensitivity" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//Aiming Variables\n" },
#endif
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aiming Variables" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bWentInBed_MetaData[] = {
		{ "Category", "Animation Variables" },
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bWentInAlmirah_MetaData[] = {
		{ "Category", "Animation Variables" },
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasFlashlight_MetaData[] = {
		{ "Category", "Flashlight Variables" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "//Flashlight Variables\n" },
#endif
		{ "ModuleRelativePath", "Player/Baoli_Character.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Flashlight Variables" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SpringArmComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CameraComponent;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FOV;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CameraLagSpeedStart;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CameraLagSpeedEnd;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CameraLagTransitionDuration;
	static void NewProp_NoCameraRotationLag_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_NoCameraRotationLag;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InspectionLightComponent;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InspectionLightIntensity;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InspectionAttenuationRadius;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MotionWarpingComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerController;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InputMapping;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LookAction;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WalkAction;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SprintAction;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_StrafeAction;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AimAction;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CrouchAction;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_JumpAction;
	static void NewProp_bWantsToSprint_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bWantsToSprint;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentGait;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CoverSystem;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MovementComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SpeedMapCurve;
	static void NewProp_bWantsToStrafe_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bWantsToStrafe;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AimSensitivity;
	static void NewProp_bWentInBed_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bWentInBed;
	static void NewProp_bWentInAlmirah_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bWentInAlmirah;
	static void NewProp_bHasFlashlight_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasFlashlight;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_ABaoli_Character_DisableCharacter, "DisableCharacter" }, // 227972803
		{ &Z_Construct_UFunction_ABaoli_Character_EnableCharacter, "EnableCharacter" }, // 3227465623
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ABaoli_Character>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABaoli_Character_Statics::NewProp_SpringArmComponent = { "SpringArmComponent", nullptr, (EPropertyFlags)0x001000000008000d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Character, SpringArmComponent), Z_Construct_UClass_USpringArmComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpringArmComponent_MetaData), NewProp_SpringArmComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABaoli_Character_Statics::NewProp_CameraComponent = { "CameraComponent", nullptr, (EPropertyFlags)0x001000000008000d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Character, CameraComponent), Z_Construct_UClass_UCameraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CameraComponent_MetaData), NewProp_CameraComponent_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ABaoli_Character_Statics::NewProp_FOV = { "FOV", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Character, FOV), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FOV_MetaData), NewProp_FOV_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ABaoli_Character_Statics::NewProp_CameraLagSpeedStart = { "CameraLagSpeedStart", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Character, CameraLagSpeedStart), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CameraLagSpeedStart_MetaData), NewProp_CameraLagSpeedStart_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ABaoli_Character_Statics::NewProp_CameraLagSpeedEnd = { "CameraLagSpeedEnd", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Character, CameraLagSpeedEnd), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CameraLagSpeedEnd_MetaData), NewProp_CameraLagSpeedEnd_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ABaoli_Character_Statics::NewProp_CameraLagTransitionDuration = { "CameraLagTransitionDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Character, CameraLagTransitionDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CameraLagTransitionDuration_MetaData), NewProp_CameraLagTransitionDuration_MetaData) };
void Z_Construct_UClass_ABaoli_Character_Statics::NewProp_NoCameraRotationLag_SetBit(void* Obj)
{
	((ABaoli_Character*)Obj)->NoCameraRotationLag = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ABaoli_Character_Statics::NewProp_NoCameraRotationLag = { "NoCameraRotationLag", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ABaoli_Character), &Z_Construct_UClass_ABaoli_Character_Statics::NewProp_NoCameraRotationLag_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NoCameraRotationLag_MetaData), NewProp_NoCameraRotationLag_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABaoli_Character_Statics::NewProp_InspectionLightComponent = { "InspectionLightComponent", nullptr, (EPropertyFlags)0x001000000008000d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Character, InspectionLightComponent), Z_Construct_UClass_UPointLightComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InspectionLightComponent_MetaData), NewProp_InspectionLightComponent_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_ABaoli_Character_Statics::NewProp_InspectionLightIntensity = { "InspectionLightIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Character, InspectionLightIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InspectionLightIntensity_MetaData), NewProp_InspectionLightIntensity_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_ABaoli_Character_Statics::NewProp_InspectionAttenuationRadius = { "InspectionAttenuationRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Character, InspectionAttenuationRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InspectionAttenuationRadius_MetaData), NewProp_InspectionAttenuationRadius_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABaoli_Character_Statics::NewProp_MotionWarpingComponent = { "MotionWarpingComponent", nullptr, (EPropertyFlags)0x001000000008000d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Character, MotionWarpingComponent), Z_Construct_UClass_UMotionWarpingComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MotionWarpingComponent_MetaData), NewProp_MotionWarpingComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABaoli_Character_Statics::NewProp_PlayerController = { "PlayerController", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Character, PlayerController), Z_Construct_UClass_ABaoli_Controller_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerController_MetaData), NewProp_PlayerController_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABaoli_Character_Statics::NewProp_InputMapping = { "InputMapping", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Character, InputMapping), Z_Construct_UClass_UInputMappingContext_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InputMapping_MetaData), NewProp_InputMapping_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABaoli_Character_Statics::NewProp_LookAction = { "LookAction", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Character, LookAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LookAction_MetaData), NewProp_LookAction_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABaoli_Character_Statics::NewProp_WalkAction = { "WalkAction", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Character, WalkAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WalkAction_MetaData), NewProp_WalkAction_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABaoli_Character_Statics::NewProp_SprintAction = { "SprintAction", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Character, SprintAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SprintAction_MetaData), NewProp_SprintAction_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABaoli_Character_Statics::NewProp_StrafeAction = { "StrafeAction", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Character, StrafeAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StrafeAction_MetaData), NewProp_StrafeAction_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABaoli_Character_Statics::NewProp_AimAction = { "AimAction", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Character, AimAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AimAction_MetaData), NewProp_AimAction_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABaoli_Character_Statics::NewProp_CrouchAction = { "CrouchAction", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Character, CrouchAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CrouchAction_MetaData), NewProp_CrouchAction_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABaoli_Character_Statics::NewProp_JumpAction = { "JumpAction", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Character, JumpAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_JumpAction_MetaData), NewProp_JumpAction_MetaData) };
void Z_Construct_UClass_ABaoli_Character_Statics::NewProp_bWantsToSprint_SetBit(void* Obj)
{
	((ABaoli_Character*)Obj)->bWantsToSprint = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ABaoli_Character_Statics::NewProp_bWantsToSprint = { "bWantsToSprint", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ABaoli_Character), &Z_Construct_UClass_ABaoli_Character_Statics::NewProp_bWantsToSprint_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bWantsToSprint_MetaData), NewProp_bWantsToSprint_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_ABaoli_Character_Statics::NewProp_CurrentGait = { "CurrentGait", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Character, CurrentGait), Z_Construct_UEnum_Baoli_EMyGait, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentGait_MetaData), NewProp_CurrentGait_MetaData) }; // 655517479
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_ABaoli_Character_Statics::NewProp_CoverSystem = { "CoverSystem", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Character, CoverSystem), Z_Construct_UEnum_Baoli_ECoverSystem, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CoverSystem_MetaData), NewProp_CoverSystem_MetaData) }; // 1059702029
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABaoli_Character_Statics::NewProp_MovementComponent = { "MovementComponent", nullptr, (EPropertyFlags)0x0010000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Character, MovementComponent), Z_Construct_UClass_UCharacterMovementComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementComponent_MetaData), NewProp_MovementComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ABaoli_Character_Statics::NewProp_SpeedMapCurve = { "SpeedMapCurve", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Character, SpeedMapCurve), Z_Construct_UClass_UCurveFloat_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpeedMapCurve_MetaData), NewProp_SpeedMapCurve_MetaData) };
void Z_Construct_UClass_ABaoli_Character_Statics::NewProp_bWantsToStrafe_SetBit(void* Obj)
{
	((ABaoli_Character*)Obj)->bWantsToStrafe = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ABaoli_Character_Statics::NewProp_bWantsToStrafe = { "bWantsToStrafe", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ABaoli_Character), &Z_Construct_UClass_ABaoli_Character_Statics::NewProp_bWantsToStrafe_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bWantsToStrafe_MetaData), NewProp_bWantsToStrafe_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ABaoli_Character_Statics::NewProp_AimSensitivity = { "AimSensitivity", nullptr, (EPropertyFlags)0x0010000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ABaoli_Character, AimSensitivity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AimSensitivity_MetaData), NewProp_AimSensitivity_MetaData) };
void Z_Construct_UClass_ABaoli_Character_Statics::NewProp_bWentInBed_SetBit(void* Obj)
{
	((ABaoli_Character*)Obj)->bWentInBed = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ABaoli_Character_Statics::NewProp_bWentInBed = { "bWentInBed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ABaoli_Character), &Z_Construct_UClass_ABaoli_Character_Statics::NewProp_bWentInBed_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bWentInBed_MetaData), NewProp_bWentInBed_MetaData) };
void Z_Construct_UClass_ABaoli_Character_Statics::NewProp_bWentInAlmirah_SetBit(void* Obj)
{
	((ABaoli_Character*)Obj)->bWentInAlmirah = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ABaoli_Character_Statics::NewProp_bWentInAlmirah = { "bWentInAlmirah", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ABaoli_Character), &Z_Construct_UClass_ABaoli_Character_Statics::NewProp_bWentInAlmirah_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bWentInAlmirah_MetaData), NewProp_bWentInAlmirah_MetaData) };
void Z_Construct_UClass_ABaoli_Character_Statics::NewProp_bHasFlashlight_SetBit(void* Obj)
{
	((ABaoli_Character*)Obj)->bHasFlashlight = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ABaoli_Character_Statics::NewProp_bHasFlashlight = { "bHasFlashlight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ABaoli_Character), &Z_Construct_UClass_ABaoli_Character_Statics::NewProp_bHasFlashlight_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasFlashlight_MetaData), NewProp_bHasFlashlight_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_ABaoli_Character_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Character_Statics::NewProp_SpringArmComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Character_Statics::NewProp_CameraComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Character_Statics::NewProp_FOV,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Character_Statics::NewProp_CameraLagSpeedStart,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Character_Statics::NewProp_CameraLagSpeedEnd,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Character_Statics::NewProp_CameraLagTransitionDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Character_Statics::NewProp_NoCameraRotationLag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Character_Statics::NewProp_InspectionLightComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Character_Statics::NewProp_InspectionLightIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Character_Statics::NewProp_InspectionAttenuationRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Character_Statics::NewProp_MotionWarpingComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Character_Statics::NewProp_PlayerController,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Character_Statics::NewProp_InputMapping,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Character_Statics::NewProp_LookAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Character_Statics::NewProp_WalkAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Character_Statics::NewProp_SprintAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Character_Statics::NewProp_StrafeAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Character_Statics::NewProp_AimAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Character_Statics::NewProp_CrouchAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Character_Statics::NewProp_JumpAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Character_Statics::NewProp_bWantsToSprint,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Character_Statics::NewProp_CurrentGait,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Character_Statics::NewProp_CoverSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Character_Statics::NewProp_MovementComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Character_Statics::NewProp_SpeedMapCurve,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Character_Statics::NewProp_bWantsToStrafe,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Character_Statics::NewProp_AimSensitivity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Character_Statics::NewProp_bWentInBed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Character_Statics::NewProp_bWentInAlmirah,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ABaoli_Character_Statics::NewProp_bHasFlashlight,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ABaoli_Character_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_ABaoli_Character_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_ACharacter,
	(UObject* (*)())Z_Construct_UPackage__Script_Baoli,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ABaoli_Character_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_ABaoli_Character_Statics::ClassParams = {
	&ABaoli_Character::StaticClass,
	"Game",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_ABaoli_Character_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_ABaoli_Character_Statics::PropPointers),
	0,
	0x009000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ABaoli_Character_Statics::Class_MetaDataParams), Z_Construct_UClass_ABaoli_Character_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ABaoli_Character()
{
	if (!Z_Registration_Info_UClass_ABaoli_Character.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ABaoli_Character.OuterSingleton, Z_Construct_UClass_ABaoli_Character_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ABaoli_Character.OuterSingleton;
}
template<> BAOLI_API UClass* StaticClass<ABaoli_Character>()
{
	return ABaoli_Character::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(ABaoli_Character);
ABaoli_Character::~ABaoli_Character() {}
// End Class ABaoli_Character

// Begin Registration
struct Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_Player_Baoli_Character_h_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EMyGait_StaticEnum, TEXT("EMyGait"), &Z_Registration_Info_UEnum_EMyGait, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 655517479U) },
		{ ECoverSystem_StaticEnum, TEXT("ECoverSystem"), &Z_Registration_Info_UEnum_ECoverSystem, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1059702029U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_ABaoli_Character, ABaoli_Character::StaticClass, TEXT("ABaoli_Character"), &Z_Registration_Info_UClass_ABaoli_Character, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ABaoli_Character), 44350422U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_Player_Baoli_Character_h_1885816939(TEXT("/Script/Baoli"),
	Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_Player_Baoli_Character_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_Player_Baoli_Character_h_Statics::ClassInfo),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_Player_Baoli_Character_h_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Baoli_Source_Baoli_Player_Baoli_Character_h_Statics::EnumInfo));
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
