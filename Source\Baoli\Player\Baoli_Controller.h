// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Baoli_Character.h"
#include "GameFramework/PlayerController.h"
#include "Baoli_Controller.generated.h"

UENUM(BlueprintType)
enum class ECharacterState : uint8
{
	OverWorld UMETA(DisplayName = "OverWorld"),
	Mechanic UMETA(DisplayName = "Mechanic"),
	GameMenu UMETA(DisplayName = "Game Menu")
};

UCLASS()
class BAOLI_API ABaoli_Controller : public APlayerController
{
	GENERATED_BODY()
public:
	/////////////////////////////////////////////////// Refrences //////////////////////////////////////////////////////

	UPROPERTY(BlueprintReadOnly, Category="Refrences")
	AB<PERSON><PERSON>_Character* Baoli_Character;

	UPROPERTY(BlueprintReadWrite, Category="Refrences")
	USkeletalMeshComponent* Mesh;

	UPROPERTY(BlueprintReadWrite, Category="Refrences")
	UCameraComponent* Cam;

	///////////////////////////////////////////////////// Variables ////////////////////////////////////////////////////

	UPROPERTY(BlueprintReadWrite, Category="Variables")
	AActor* FocusedObject;

	UPROPERTY(BlueprintReadWrite, Category="Variables")
	UPrimitiveComponent* ActiveComponent;

	UPROPERTY(BlueprintReadWrite, Category="Variables")
	bool bHoldTrace;

	UPROPERTY(BlueprintReadWrite, Category="Variables")
	float TraceDistance = 5000.0f;

	UPROPERTY(BlueprintReadWrite, Category="Variables")
	FHitResult hitResult;

	UPROPERTY(BlueprintReadOnly, Category="Variables")
	bool bLineHit;

	UPROPERTY(BlueprintReadWrite, Category="Variables")
	bool bIsInspecting;

	UPROPERTY(BlueprintReadWrite, Category="Variables")
	double SetFocalDistance;

	UPROPERTY(BlueprintReadWrite, Category="Variables")
	bool SkillCheckResult;

	UPROPERTY(BlueprintReadWrite, Category="Variables")
	bool bDrawWidget = false;

	//Input Actions & Functions
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = Input, meta = (AllowPrivateAccess = "true"))
	UInputAction* MoveAction;

	UPROPERTY(BlueprintReadWrite, Category="Variables")
	TArray<AActor*> IgnoreLineTraceActors;
	

	///////////////////////////////////////////////////// Functions ////////////////////////////////////////////////////

	void SetupInput();

	// CPP only functions
	void MoveTriggeredInput(const FInputActionValue& Value);
	void MoveStartedInput(const FInputActionValue& Value);
	//---

	UFUNCTION(BlueprintCallable)
	void TraceForInteraction();

	UFUNCTION(BlueprintCallable)
	void TraceForCamera();

	UFUNCTION(BlueprintCallable)
	void LineTrace();

	//LineTrace Checklist
	bool ShouldTrace();

	// Get current state of character
	UFUNCTION(BlueprintCallable)
	const ECharacterState& GetCharacterState() const { return CharacterState; }

	const ECharacterState* GetCharacterStatePtr() const { return &CharacterState; }

	UFUNCTION(BlueprintCallable)
	void ChangeCharacterState(ECharacterState pCharacterState);

	UFUNCTION(BlueprintImplementableEvent)
	void MovementInput(FVector2D Value, const UInputAction* Action);

protected:
	//Standard Overide Functions
	virtual void BeginPlay() override;
	virtual void Tick(float DeltaSeconds) override;

protected:
	UPROPERTY(BlueprintReadWrite, Category="Variables")
	ECharacterState CharacterState = ECharacterState::OverWorld;
};
