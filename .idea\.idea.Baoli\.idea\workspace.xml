<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoGeneratedRunConfigurationManager">
    <projectFile>D:/UE_5.5/Engine/Source/Programs/AutomationTool/AutomationTool.csproj</projectFile>
    <projectFile profileName="EpicGames.AspNet">D:/UE_5.5/Engine/Source/Programs/Shared/EpicGames.AspNet/EpicGames.AspNet.csproj</projectFile>
    <projectFile kind="Docker">D:/UE_5.5/Engine/Source/Programs/Shared/EpicGames.Perforce.Fixture/EpicGames.Perforce.Fixture.csproj</projectFile>
    <projectFile>D:/UE_5.5/Engine/Source/Programs/UnrealBuildTool/UnrealBuildTool.csproj</projectFile>
    <projectFile>Intermediate/ProjectFiles/Baoli.vcxproj</projectFile>
    <projectFile>Intermediate/ProjectFiles/UE5.vcxproj</projectFile>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="72579538-3946-4bb1-b78c-832fa1b2d415" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/Config/DefaultEngine.ini" beforeDir="false" afterPath="$PROJECT_DIR$/Config/DefaultEngine.ini" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Source/Baoli/Player/Baoli_Character.cpp" beforeDir="false" afterPath="$PROJECT_DIR$/Source/Baoli/Player/Baoli_Character.cpp" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DpaMonitoringSettings">
    <option name="firstShow" value="false" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://D:/UE_5.5/Engine/Source/Runtime/Engine/Classes/Animation/AnimInstance.h" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://D:/UE_5.5/Engine/Source/Runtime/Engine/Classes/GameFramework/Pawn.h" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Source/Baoli/Player/Baoli_Character.cpp" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Source/Baoli/Player/Baoli_Character.h" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Source/Baoli/Player/Baoli_Controller.cpp" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Source/Baoli/Player/Baoli_Controller.h" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="PerforceDirect.Settings">
    <option name="port" value="100.113.153.17:1666" />
    <option name="user" value="super" />
    <option name="CHARSET" value="utf8" />
  </component>
  <component name="PerforceNumberNameSynchronizer">
    <option name="listMappings">
      <map>
        <entry>
          <key>
            <ConnectionKeyBean>
              <option name="server" value="100.113.153.17:1666" />
              <option name="client" value="super_dev" />
              <option name="user" value="super" />
            </ConnectionKeyBean>
          </key>
          <value>
            <PerforceNumberNameMap />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2xirjAIWXowy7lMh085YPixqt1w" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;C/C++ Project.Baoli.executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;vcs.Perforce&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected="C/C++ Project.Baoli">
    <configuration name="Baoli" type="CppProject" factoryName="C++ Project">
      <configuration_1 setup="1">
        <option name="CONFIGURATION" value="Development_Editor" />
        <option name="PLATFORM" value="x64" />
        <option name="CURRENT_LAUNCH_PROFILE" value="Local" />
        <option name="EXE_PATH" value="$(LocalDebuggerCommand)" />
        <option name="PROGRAM_PARAMETERS" value="$(LocalDebuggerCommandArguments)" />
        <option name="WORKING_DIRECTORY" value="$(LocalDebuggerWorkingDirectory)" />
        <option name="PASS_PARENT_ENVS" value="1" />
        <option name="USE_EXTERNAL_CONSOLE" value="0" />
        <option name="TERMINAL_INTERACTION_BEHAVIOR" value="AUTO_DETECT" />
        <option name="PROJECT_FILE_PATH" value="$PROJECT_DIR$/Intermediate/ProjectFiles/Baoli.vcxproj" />
      </configuration_1>
      <option name="DEFAULT_PROJECT_PATH" value="$PROJECT_DIR$/Intermediate/ProjectFiles/Baoli.vcxproj" />
      <option name="PROJECT_FILE_PATH" value="$PROJECT_DIR$/Intermediate/ProjectFiles/Baoli.vcxproj" />
      <option name="AUTO_SELECT_PRIORITY" value="10010" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="UE5" type="CppProject" factoryName="C++ Project">
      <option name="DEFAULT_PROJECT_PATH" value="$PROJECT_DIR$/Intermediate/ProjectFiles/UE5.vcxproj" />
      <option name="PROJECT_FILE_PATH" value="$PROJECT_DIR$/Intermediate/ProjectFiles/UE5.vcxproj" />
      <option name="AUTO_SELECT_PRIORITY" value="10000" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="AutomationTool" type="DotNetProject" factoryName=".NET Project">
      <option name="EXE_PATH" value="" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="D:/UE_5.5/Engine/Source/Programs/AutomationTool/AutomationTool.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="UnrealBuildTool" type="DotNetProject" factoryName=".NET Project">
      <option name="EXE_PATH" value="" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="D:/UE_5.5/Engine/Source/Programs/UnrealBuildTool/UnrealBuildTool.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="EpicGames.AspNet" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="D:/UE_5.5/Engine/Source/Programs/Shared/EpicGames.AspNet/EpicGames.AspNet.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0" />
      <option name="LAUNCH_PROFILE_NAME" value="EpicGames.AspNet" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="EpicGames.Perforce.Fixture/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="containerName" value="epicgames.perforce.fixture" />
          <option name="contextFolderPath" value="H:\P4\dev\Baoli" />
          <option name="sourceFilePath" value="D:/UE_5.5/Engine/Source/Programs/Shared/EpicGames.Perforce.Fixture/Dockerfile" />
        </settings>
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="false" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="docker-deploy" factoryName="dockerfile" temporary="true">
      <deployment type="dockerfile">
        <settings />
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="false" />
      <method v="2" />
    </configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="72579538-3946-4bb1-b78c-832fa1b2d415" name="Changes" comment="" />
      <created>1748431878576</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748431878576</updated>
      <workItem from="1748431885486" duration="787000" />
      <workItem from="1749056603163" duration="2910000" />
      <workItem from="1749100324420" duration="6327000" />
      <workItem from="1749124513506" duration="2027000" />
      <workItem from="1749128674919" duration="3684000" />
      <workItem from="1749187772055" duration="4223000" />
      <workItem from="1749222554900" duration="4011000" />
      <workItem from="1749624927626" duration="12117000" />
      <workItem from="1749873961807" duration="14300000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityProjectConfiguration" hasMinimizedUI="false" />
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.OperationCanceledException" breakIfHandledByOtherCode="false" displayValue="System.OperationCanceledException" />
          <option name="timeStamp" value="1" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.Tasks.TaskCanceledException" breakIfHandledByOtherCode="false" displayValue="System.Threading.Tasks.TaskCanceledException" />
          <option name="timeStamp" value="2" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.ThreadAbortException" breakIfHandledByOtherCode="false" displayValue="System.Threading.ThreadAbortException" />
          <option name="timeStamp" value="3" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>