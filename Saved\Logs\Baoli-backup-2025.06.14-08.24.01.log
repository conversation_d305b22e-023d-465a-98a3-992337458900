﻿Log file open, 06/14/25 13:45:34
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=46064)
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: Baoli
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Display Control listening on port 1985
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.5-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.5.4-40574608+++UE5+Release-5.5"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (23H2) [10.0.22631.4751] "
LogCsvProfiler: Display: Metadata set : cpu="AuthenticAMD|AMD Ryzen 9 5950X 16-Core Processor"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" H:\P4\dev\Baoli\Baoli.uproject -skipcompile""
LogCsvProfiler: Display: Metadata set : loginid="5eff80b14364fb2f37e5468dbd2f7de6"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.261356
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +5:30, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-476D162C4E131A3BB0F90BB77086F4FE
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [H:/P4/dev/Baoli/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogConfig: Display: Loading VulkanPC ini files took 0.04 seconds
LogConfig: Display: Loading Android ini files took 0.04 seconds
LogConfig: Display: Loading IOS ini files took 0.05 seconds
LogPluginManager: Found matching target receipt: H:/P4/dev/Baoli/Binaries/Win64/BaoliEditor.target
LogConfig: Display: Loading Mac ini files took 0.05 seconds
LogPluginManager: Looking for enabled plugins target receipt
LogConfig: Display: Loading TVOS ini files took 0.05 seconds
LogConfig: Display: Loading Windows ini files took 0.06 seconds
LogConfig: Display: Loading VisionOS ini files took 0.06 seconds
LogConfig: Display: Loading Unix ini files took 0.06 seconds
LogConfig: Display: Loading LinuxArm64 ini files took 0.06 seconds
LogConfig: Display: Loading Linux ini files took 0.06 seconds
LogPluginManager: Found matching target receipt: H:/P4/dev/Baoli/Binaries/Win64/BaoliEditor.target
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin Chooser
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin JsonBlueprintUtilities
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin PCG
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin ActorPalette
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin GPULightmass
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin StudioTelemetry
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin DataRegistry
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GameFeatures
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GeometryScripting
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin ModularGameplay
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin ScriptableToolsFramework
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WebBrowserWidget
LogPluginManager: Mounting Engine plugin USDCore
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin AnimationWarping
LogPluginManager: Mounting Engine plugin AnimationLocomotionLibrary
LogPluginManager: Mounting Engine plugin BlendStack
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin LiveLink
LogPluginManager: Mounting Engine plugin GameplayInsights
LogPluginManager: Mounting Engine plugin MotionWarping
LogPluginManager: Mounting Engine plugin PoseSearch
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin GameplayCameras
LogAssetRegistry: Display: Asset registry cache read as 106.4 MiB from H:/P4/dev/Baoli/Intermediate/CachedAssetRegistry_0.bin
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin AssetReferenceRestrictions
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin ScriptableToolsEditorMode
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin AxFImporter
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin NiagaraFluids
LogPluginManager: Mounting Engine plugin USDImporter
LogPluginManager: Mounting Engine plugin MDLImporter
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin PCGGeometryScriptInterop
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin HoldoutComposite
LogPluginManager: Mounting Engine plugin AnalyticsBlueprintLibrary
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin RiderLink
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin SQLiteCore
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin ConcertMain
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin Reflex
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin MotionTrajectory
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin ConcertSyncClient
LogPluginManager: Mounting Engine plugin ConcertSyncCore
LogPluginManager: Mounting Project plugin Inkpot
LogPluginManager: Mounting Project plugin OptimizedWebBrowser
LogPluginManager: Mounting Project plugin rdBPtools
LogPluginManager: Mounting Project plugin SnappingHelper
LogPluginManager: Mounting Project plugin PlatformFunctions
SourceControl: Revision control is disabled
LogGPULightmass: GPULightmass module is loaded
LogNFORDenoise: NFORDenoise function starting up
LogStudioTelemetry: Display: Starting StudioTelemetry Module
LogStudioTelemetry: Started StudioTelemetry Session
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogEOSSDK: Initializing EOSSDK Version:1.17.0-39599718
LogInit: Using libcurl 8.4.0
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum 2147483647 concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.5-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=5eff80b14364fb2f37e5468dbd2f7de6
LogInit: DeviceId=
LogInit: Engine Version: 5.5.4-40574608+++UE5+Release-5.5
LogInit: Compatible Engine Version: 5.5.0-37670630+++UE5+Release-5.5
LogInit: Net CL: 37670630
LogInit: OS: Windows 11 (23H2) [10.0.22631.4751] (), CPU: AMD Ryzen 9 5950X 16-Core Processor            , GPU: AMD Radeon RX 6900 XT
LogInit: Compiled (64-bit): Mar  7 2025 14:49:53
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.5
LogInit: Command Line: -skipcompile
LogInit: Base Directory: D:/UE_5.5/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 36
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 170
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 15
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 10
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 119
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 51
LogDevObjectVersion:   UE5-PrivateFrosty (59DA5D52-1232-4948-B878-597870B8E98B): 8
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogConfig: Branch 'EditorLayout' had been unloaded. Reloading on-demand took 0.57ms
LogConfig: Branch 'Bridge' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosCloth' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Chooser' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CmdLinkServer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'FastBuildController' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'JsonBlueprintUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshPainting' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RenderGraphInsights' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'UbaController' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WorldMetrics' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'XGEController' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ActorPalette' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AdvancedRenamer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AutomationUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BackChannel' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosCaching' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosNiagara' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosUserDataPT' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CharacterAI' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosSolverPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Dataflow' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorDataStorage' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorPerformance' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorTelemetry' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Fracture' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryCollectionPlugin' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'GeometryFlow' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GPULightmass' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LowLevelNetTrace' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LocalizableMessage' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshModelingToolsetExp' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NFORDenoise' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlanarCut' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlatformCrypto' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PythonScriptPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SkeletalReduction' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'StudioTelemetry' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'TcpMessaging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UdpMessaging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NNERuntimeORT' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NNEDenoiser' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ActorLayerUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidFileServer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidPermission' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleImageUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ArchVisCharacter' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AssetTags' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioCapture' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioWidgets' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChunkDownloader' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CableComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ComputeFramework' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'DataRegistry' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ExampleDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GameFeatures' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioSynesthesia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CustomMeshComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryProcessing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryScripting' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryCache' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GooglePAD' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GoogleCloudMessaging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'InputDebugging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'IOSDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.10ms
LogConfig: Branch 'LocationServicesBPLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LinuxDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshModelingToolset' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MobilePatchingUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ModularGameplay' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MsQuic' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ProceduralMeshComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PropertyAccessEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ResonanceAudio' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ScriptableToolsFramework' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SignificanceManager' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SoundFields' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WaveTable' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WebBrowserWidget' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WebMMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WindowsDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WindowsMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AISupport' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EnvironmentQueryEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ACLPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationData' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationModifierLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationWarping' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AnimationLocomotionLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BlendSpaceMotionAnalysis' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ControlRigSpline' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'ControlRigModules' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GameplayInsights' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MotionWarping' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PoseSearch' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RigLogic' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CameraShakePreviewer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EngineCameras' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OodleNetwork' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationSharing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CodeLiteSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'DumpGPUServices' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GitSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'KDevelopSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CLionSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'N10XSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NullSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PerforceSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PixWinPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PropertyAccessNode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PluginUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PlasticSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RenderDocPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SubversionSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RiderSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'TextureFormatOodle' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UObjectPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'VisualStudioCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AssetManagerEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'VisualStudioSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AssetReferenceRestrictions' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BlueprintHeaderView' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChangelistReview' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ColorGrading' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CryptoKeys' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CurveEditorTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorDebugTools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'DataValidation' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EngineAssetDefinitions' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'FacialAnimation' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryMode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GameplayTagsEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MacGraphicsSwitching' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MaterialAnalyzer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshLODToolset' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MobileLauncherProfileWizard' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ModelingToolsEditorMode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PluginBrowser' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ScriptableToolsEditorMode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ProxyLODPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SpeedTreeImporter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SequencerAnimTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UVEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UMGWidgetPreview' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'StylusInput' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WorldPartitionHLODUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AxFImporter' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'VariantManager' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NiagaraSimCaching' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AlembicImporter' had been unloaded. Reloading on-demand took 0.07ms
LogCoreRedirects: AddRedirect(H:/P4/dev/Baoli/Saved/Config/WindowsEditor/NiagaraFluids.ini) has wildcard redirect /NiagaraSimulationStages/, these are very slow and should be resolved as soon as possible! Please refer to the documentation in Engine/Config/BaseEngine.ini.
LogConfig: Branch 'USDImporter' had been unloaded. Reloading on-demand took 0.08ms
LogCoreRedirects: AddRedirect(H:/P4/dev/Baoli/Saved/Config/WindowsEditor/MDLImporter.ini) has wildcard redirect /DatasmithContent/Materials/MDL/, these are very slow and should be resolved as soon as possible! Please refer to the documentation in Engine/Config/BaseEngine.ini.
LogConfig: Branch 'InterchangeEditor' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'AndroidMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AvfMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MediaCompositing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MediaPlate' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ImgMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MediaPlayerEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WebMMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ActorSequence' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WmfMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LevelSequenceEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'TemplateSequence' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SequencerScripting' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EOSShared' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineBase' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineServices' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystem' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystemNull' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LauncherChunkInstaller' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'InterchangeTests' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AnalyticsBlueprintLibrary' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'PortableObjectFileDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RiderLink' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SQLiteCore' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XInputDevice' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ConcertMain' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemGooglePlay' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemIOS' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LightMixer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ObjectMixer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Reflex' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserAssetDataSource' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ContentBrowserClassDataSource' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ContentBrowserFileDataSource' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'SkeletalMeshModelingTools' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'MotionTrajectory' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BaseCharacterFXEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ConcertSyncClient' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Inkpot' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OptimizedWebBrowser' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'rdBPtools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SnappingHelper' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'PlatformFunctions' had been unloaded. Reloading on-demand took 0.07ms
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogStreaming: Display: AsyncLoading2 - Created: Event Driven Loader: false, Async Loading Thread: false, Async Post Load: false
LogStreaming: Display: AsyncLoading2 - Initialized
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: Set CVar [[r.Nanite.Streaming.AsyncCompute:0	; Temporary workaround for Nanite geometry corruption (FORT-805141)]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.DynamicRes.DynamicFrameTime:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.RHICmdBypass:0]]
[2025.06.14-08.15.34:965][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.06.14-08.15.34:965][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.06.14-08.15.34:965][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.06.14-08.15.34:965][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.06.14-08.15.34:965][  0]LogConfig: CVar [[r.Mobile.EnableNoPrecomputedLightingCSMShader:1]] deferred - dummy variable created
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.GPUSkin.Support16BitBoneIndex:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.GPUSkin.UnlimitedBoneInfluences:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: CVar [[SkeletalMesh.UseExperimentalChunking:1]] deferred - dummy variable created
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.AllowGlobalClipPlane:0]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.CustomDepth:3]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.VirtualTextures:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.AntiAliasingMethod:4]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.ReflectionCaptureResolution:32]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs:0]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.RayTracing:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.Lumen.Reflections.HardwareRayTracing.Translucent.Refraction.EnableForProject:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.MegaLights.EnableForProject:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.PathTracing:0]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.RayTracing.Shadows:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.RayTracing.UseTextureLod:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.EnableForProject:0]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.CustomDepthTemporalAAJitter:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.ScreenPercentage.Default:75.000000]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.ScreenPercentage.Default.Desktop.Mode:0]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.Shadow.CSMCaching:0]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.MSAACount:8]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.TileSize:32]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.TileBorderSize:2]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.MaxTextureSize:4096]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.UseCompression:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.VT.TileBorderSize:4]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.MeshPaintVirtualTexture.DefaultTexelsPerVertex:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.AllowStaticLighting:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.Shadow.UnbuiltPreviewInGame:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.NormalMapsForStaticLighting:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.Lumen.HardwareRayTracing.LightingMode:0]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.VirtualTexturedLightmaps:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.Lumen.HardwareRayTracing:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.Lumen.ScreenTracingSource:0]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure:0]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.Bias:0.000000]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.Method:2]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.vt.rvt.HighQualityPerPixelHeight:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.06.14-08.15.34:965][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.06.14-08.15.34:965][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.06.14-08.15.34:965][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.06.14-08.15.34:965][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.06.14-08.15.34:965][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.06.14-08.15.34:965][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.06.14-08.15.34:965][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.06.14-08.15.34:965][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.06.14-08.15.34:965][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.06.14-08.15.34:965][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.06.14-08.15.34:965][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.06.14-08.15.34:965][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.06.14-08.15.34:965][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.06.14-08.15.34:965][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.06.14-08.15.34:965][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.06.14-08.15.34:965][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.06.14-08.15.34:965][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.06.14-08.15.34:965][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.06.14-08.15.34:965][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.06.14-08.15.34:965][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.06.14-08.15.34:965][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.06.14-08.15.34:965][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.06.14-08.15.34:965][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.06.14-08.15.34:965][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.06.14-08.15.34:965][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.06.14-08.15.34:965][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.06.14-08.15.34:965][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.06.14-08.15.34:965][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.06.14-08.15.34:966][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.06.14-08.15.34:966][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.06.14-08.15.34:966][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.06.14-08.15.34:966][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.06.14-08.15.34:966][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.06.14-08.15.34:966][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.06.14-08.15.34:966][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.06.14-08.15.34:966][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.06.14-08.15.34:966][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.06.14-08.15.34:966][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.06.14-08.15.34:966][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.06.14-08.15.34:966][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.06.14-08.15.34:966][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.06.14-08.15.34:966][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.06.14-08.15.34:966][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.06.14-08.15.34:966][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.06.14-08.15.34:966][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.06.14-08.15.34:966][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.06.14-08.15.34:966][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.06.14-08.15.34:966][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.06.14-08.15.34:966][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.06.14-08.15.34:966][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.06.14-08.15.34:966][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.06.14-08.15.34:966][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.06.14-08.15.34:966][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.06.14-08.15.34:970][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.06.14-08.15.34:970][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:300]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:200]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.SkyLight.RealTimeReflectionCapture:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.TonemapMode:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.06.14-08.15.34:970][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:512]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:512]]
[2025.06.14-08.15.34:970][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.06.14-08.15.34:970][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.06.14-08.15.34:970][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.06.14-08.15.34:970][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.06.14-08.15.34:973][  0]LogRHI: Using Default RHI: D3D12
[2025.06.14-08.15.34:973][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.06.14-08.15.34:973][  0]LogRHI: Loading RHI module D3D12RHI
[2025.06.14-08.15.34:978][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.06.14-08.15.34:978][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.06.14-08.15.35:107][  0]LogD3D12RHI: Found D3D12 adapter 0: AMD Radeon RX 6900 XT (VendorId: 1002, DeviceId: 73af, SubSysId: e3a1002, Revision: 00c0
[2025.06.14-08.15.35:107][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.06.14-08.15.35:107][  0]LogD3D12RHI:   Adapter has 16337MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 3 output[s]
[2025.06.14-08.15.35:107][  0]LogD3D12RHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.06.14-08.15.35:107][  0]LogD3D12RHI:      Driver Date: 4-25-2025
[2025.06.14-08.15.35:170][  0]LogD3D12RHI: D3D12CreateDevice failed with code 0x8007000E
[2025.06.14-08.15.35:175][  0]LogD3D12RHI: Found D3D12 adapter 2: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.06.14-08.15.35:175][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
[2025.06.14-08.15.35:175][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 0 output[s]
[2025.06.14-08.15.35:294][  0]LogD3D12RHI: Found D3D12 adapter 3: AMD Radeon RX 6900 XT (VendorId: 1002, DeviceId: 73af, SubSysId: e3a1002, Revision: 00c0
[2025.06.14-08.15.35:294][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.06.14-08.15.35:294][  0]LogD3D12RHI:   Adapter has 16337MB of dedicated video memory, 0MB of dedicated system memory, and 32725MB of shared system memory, 0 output[s]
[2025.06.14-08.15.35:294][  0]LogD3D12RHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.06.14-08.15.35:294][  0]LogD3D12RHI:      Driver Date: 4-25-2025
[2025.06.14-08.15.35:294][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.06.14-08.15.35:294][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.06.14-08.15.35:294][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.06.14-08.15.35:294][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.06.14-08.15.35:294][  0]LogHAL: Display: Platform has ~ 64 GB [68630138880 / 68719476736 / 64], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.06.14-08.15.35:294][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.06.14-08.15.35:294][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.06.14-08.15.35:294][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.06.14-08.15.35:294][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.06.14-08.15.35:294][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.06.14-08.15.35:295][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.06.14-08.15.35:295][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.06.14-08.15.35:295][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.06.14-08.15.35:295][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.06.14-08.15.35:295][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.06.14-08.15.35:295][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.06.14-08.15.35:295][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.06.14-08.15.35:295][  0]LogConfig: Set CVar [[memory.MemoryPressureCriticalThresholdMB:512]]
[2025.06.14-08.15.35:295][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [H:/P4/dev/Baoli/Saved/Config/WindowsEditor/Editor.ini]
[2025.06.14-08.15.35:295][  0]LogInit: Computer: DESKTOP-E41IK6R
[2025.06.14-08.15.35:295][  0]LogInit: User: Shashank
[2025.06.14-08.15.35:295][  0]LogInit: CPU Page size=4096, Cores=16
[2025.06.14-08.15.35:295][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.06.14-08.15.36:089][  0]LogMemory: Memory total: Physical=63.9GB (64GB approx) Virtual=70.1GB
[2025.06.14-08.15.36:089][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.06.14-08.15.36:089][  0]LogMemory: Process Physical Memory: 685.00 MB used, 740.20 MB peak
[2025.06.14-08.15.36:089][  0]LogMemory: Process Virtual Memory: 811.85 MB used, 811.85 MB peak
[2025.06.14-08.15.36:089][  0]LogMemory: Physical Memory: 34004.26 MB used,  31446.54 MB free, 65450.80 MB total
[2025.06.14-08.15.36:089][  0]LogMemory: Virtual Memory: 48926.41 MB used,  22831.48 MB free, 71757.88 MB total
[2025.06.14-08.15.36:089][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.06.14-08.15.36:095][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.06.14-08.15.36:102][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.06.14-08.15.36:102][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.06.14-08.15.36:103][  0]LogInit: Using OS detected language (en-GB).
[2025.06.14-08.15.36:103][  0]LogInit: Using OS detected locale (en-IN).
[2025.06.14-08.15.36:105][  0]LogTextLocalizationManager: No specific localization for 'en-GB' exists, so 'en' will be used for the language.
[2025.06.14-08.15.36:105][  0]LogInit: Setting process to per monitor DPI aware
[2025.06.14-08.15.36:390][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.06.14-08.15.36:390][  0]LogWindowsTextInputMethodSystem:   - English (United States) - (Keyboard).
[2025.06.14-08.15.36:390][  0]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.06.14-08.15.36:403][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.06.14-08.15.36:403][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.06.14-08.15.36:522][  0]LogRHI: Using Default RHI: D3D12
[2025.06.14-08.15.36:522][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.06.14-08.15.36:522][  0]LogRHI: Loading RHI module D3D12RHI
[2025.06.14-08.15.36:522][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.06.14-08.15.36:522][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.06.14-08.15.36:522][  0]LogD3D12RHI: Display: Creating D3D12 RHI with Max Feature Level SM6
[2025.06.14-08.15.36:523][  0]LogWindows: Attached monitors:
[2025.06.14-08.15.36:523][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY5' [PRIMARY]
[2025.06.14-08.15.36:523][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY6'
[2025.06.14-08.15.36:523][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY7'
[2025.06.14-08.15.36:523][  0]LogWindows: Found 3 attached monitors.
[2025.06.14-08.15.36:523][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.06.14-08.15.36:523][  0]LogRHI: RHI Adapter Info:
[2025.06.14-08.15.36:523][  0]LogRHI:             Name: AMD Radeon RX 6900 XT
[2025.06.14-08.15.36:523][  0]LogRHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.06.14-08.15.36:523][  0]LogRHI:      Driver Date: 4-25-2025
[2025.06.14-08.15.36:523][  0]LogD3D12RHI:     GPU DeviceId: 0x73af (for the marketing name, search the web for "GPU Device Id")
[2025.06.14-08.15.36:561][  0]LogD3D12RHI: InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
[2025.06.14-08.15.36:630][  0]LogNvidiaAftermath: Aftermath initialized
[2025.06.14-08.15.36:630][  0]LogD3D12RHI: Emitting draw events for PIX profiling.
[2025.06.14-08.15.36:730][  0]LogNvidiaAftermath: Warning: Skipping aftermath initialization on non-Nvidia device.
[2025.06.14-08.15.36:730][  0]LogD3D12RHI: ID3D12Device1 is supported.
[2025.06.14-08.15.36:730][  0]LogD3D12RHI: ID3D12Device2 is supported.
[2025.06.14-08.15.36:730][  0]LogD3D12RHI: ID3D12Device3 is supported.
[2025.06.14-08.15.36:730][  0]LogD3D12RHI: ID3D12Device4 is supported.
[2025.06.14-08.15.36:730][  0]LogD3D12RHI: ID3D12Device5 is supported.
[2025.06.14-08.15.36:730][  0]LogD3D12RHI: ID3D12Device6 is supported.
[2025.06.14-08.15.36:730][  0]LogD3D12RHI: ID3D12Device7 is supported.
[2025.06.14-08.15.36:730][  0]LogD3D12RHI: ID3D12Device8 is supported.
[2025.06.14-08.15.36:730][  0]LogD3D12RHI: ID3D12Device9 is supported.
[2025.06.14-08.15.36:730][  0]LogD3D12RHI: ID3D12Device10 is supported.
[2025.06.14-08.15.36:730][  0]LogD3D12RHI: ID3D12Device11 is supported.
[2025.06.14-08.15.36:730][  0]LogD3D12RHI: ID3D12Device12 is supported.
[2025.06.14-08.15.36:730][  0]LogD3D12RHI: Bindless resources are supported
[2025.06.14-08.15.36:730][  0]LogD3D12RHI: Stencil ref from pixel shader is supported
[2025.06.14-08.15.36:730][  0]LogD3D12RHI: Raster order views are supported
[2025.06.14-08.15.36:730][  0]LogD3D12RHI: Wave Operations are supported (wave size: min=32 max=64).
[2025.06.14-08.15.36:730][  0]LogD3D12RHI: D3D12 ray tracing tier 1.1 and bindless resources are supported.
[2025.06.14-08.15.36:730][  0]LogD3D12RHI: Mesh shader tier 1.0 is supported
[2025.06.14-08.15.36:730][  0]LogD3D12RHI: AtomicInt64OnTypedResource is supported
[2025.06.14-08.15.36:730][  0]LogD3D12RHI: AtomicInt64OnGroupShared is supported
[2025.06.14-08.15.36:730][  0]LogD3D12RHI: AtomicInt64OnDescriptorHeapResource is supported
[2025.06.14-08.15.36:730][  0]LogD3D12RHI: Shader Model 6.6 atomic64 is supported
[2025.06.14-08.15.36:754][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000005104E365300)
[2025.06.14-08.15.36:754][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000005104E365580)
[2025.06.14-08.15.36:755][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000005104E365800)
[2025.06.14-08.15.36:755][  0]LogD3D12RHI: Display: Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
[2025.06.14-08.15.36:755][  0]LogD3D12RHI: Display: Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
[2025.06.14-08.15.36:755][  0]LogD3D12RHI: AMD hit token extension is not supported
[2025.06.14-08.15.36:755][  0]LogRHI: Texture pool is 9808 MB (70% of 14012 MB)
[2025.06.14-08.15.36:755][  0]LogD3D12RHI: Async texture creation enabled
[2025.06.14-08.15.36:755][  0]LogD3D12RHI: RHI has support for 64 bit atomics
[2025.06.14-08.15.36:765][  0]LogVRS: Current RHI supports per-draw and screenspace Variable Rate Shading
[2025.06.14-08.15.36:769][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.06.14-08.15.36:777][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="H:/P4/dev/Baoli/Baoli.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/P4/dev/Baoli/Intermediate/TurnkeyReport_0.log" -log="H:/P4/dev/Baoli/Intermediate/TurnkeyLog_0.log" -project="H:/P4/dev/Baoli/Baoli.uproject"  -platform=all'
[2025.06.14-08.15.36:777][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/P4/dev/Baoli/Baoli.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/P4/dev/Baoli/Intermediate/TurnkeyReport_0.log" -log="H:/P4/dev/Baoli/Intermediate/TurnkeyLog_0.log" -project="H:/P4/dev/Baoli/Baoli.uproject"  -platform=all" ]
[2025.06.14-08.15.36:801][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.06.14-08.15.36:801][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.06.14-08.15.36:801][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.06.14-08.15.36:801][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.06.14-08.15.36:801][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.06.14-08.15.36:801][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.12
[2025.06.14-08.15.36:801][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.06.14-08.15.36:801][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.06.14-08.15.36:801][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.06.14-08.15.36:825][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.06.14-08.15.36:825][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.06.14-08.15.36:825][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.06.14-08.15.36:825][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.06.14-08.15.36:825][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.06.14-08.15.36:825][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.06.14-08.15.36:825][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.06.14-08.15.36:825][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.06.14-08.15.36:825][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.06.14-08.15.36:825][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.06.14-08.15.36:842][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.06.14-08.15.36:842][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.06.14-08.15.36:858][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.06.14-08.15.36:858][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.06.14-08.15.36:858][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.06.14-08.15.36:858][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.06.14-08.15.36:873][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64'
[2025.06.14-08.15.36:873][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Server'
[2025.06.14-08.15.36:873][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Client'
[2025.06.14-08.15.36:888][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.06.14-08.15.36:888][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.06.14-08.15.36:888][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.06.14-08.15.36:888][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.06.14-08.15.36:904][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.06.14-08.15.36:904][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.06.14-08.15.36:922][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.06.14-08.15.36:922][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.06.14-08.15.36:922][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.06.14-08.15.36:922][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.06.14-08.15.36:922][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.06.14-08.15.36:965][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL from hinted modules, loading all potential format modules to find it
[2025.06.14-08.15.36:968][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.06.14-08.15.36:968][  0]LogTargetPlatformManager:   SF_METAL
[2025.06.14-08.15.36:968][  0]LogTargetPlatformManager:   SF_METAL_MRT
[2025.06.14-08.15.36:968][  0]LogTargetPlatformManager:   SF_METAL_TVOS
[2025.06.14-08.15.36:968][  0]LogTargetPlatformManager:   SF_METAL_MRT_TVOS
[2025.06.14-08.15.36:968][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.06.14-08.15.36:968][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.06.14-08.15.36:968][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.06.14-08.15.36:968][  0]LogTargetPlatformManager:   SF_METAL_MACES3_1
[2025.06.14-08.15.36:968][  0]LogTargetPlatformManager:   SF_METAL_MRT_MAC
[2025.06.14-08.15.36:968][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.06.14-08.15.36:968][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.06.14-08.15.36:968][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.06.14-08.15.36:968][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.06.14-08.15.36:968][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.06.14-08.15.36:968][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.06.14-08.15.36:968][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.06.14-08.15.36:968][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.06.14-08.15.36:968][  0]LogTargetPlatformManager:   VVM_1_0
[2025.06.14-08.15.36:968][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.06.14-08.15.36:968][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.06.14-08.15.36:968][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.06.14-08.15.36:968][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.06.14-08.15.36:968][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.06.14-08.15.36:968][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.06.14-08.15.36:968][  0]LogRendererCore: Ray tracing is enabled (dynamic). Reason: r.RayTracing=1 and r.RayTracing.EnableOnDemand=1.
[2025.06.14-08.15.36:968][  0]LogRendererCore: Ray tracing shaders are enabled.
[2025.06.14-08.15.36:970][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.06.14-08.15.36:970][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file H:/P4/dev/Baoli/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.06.14-08.15.36:970][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.06.14-08.15.36:970][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file H:/P4/dev/Baoli/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.06.14-08.15.36:970][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.06.14-08.15.37:027][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1559 MiB)
[2025.06.14-08.15.37:027][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.06.14-08.15.37:027][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.06.14-08.15.37:027][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.06.14-08.15.37:028][  0]LogZenServiceInstance: InTree version at 'D:/UE_5.5/Engine/Binaries/Win64/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.06.14-08.15.37:028][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.06.14-08.15.37:028][  0]LogZenServiceInstance: No current process using the data dir found, launching a new instance
[2025.06.14-08.15.37:028][  0]LogZenServiceInstance: Display: Launching executable 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe', working dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install', data dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data', args '--port 8558 --data-dir "C:\Users\<USER>\AppData\Local\UnrealEngine\Common\Zen\Data" --http asio --gc-cache-duration-seconds 1209600 --gc-interval-seconds 21600 --gc-low-diskspace-threshold 2147483648 --quiet --http-forceloopback --owner-pid 46348 --child-id Zen_46348_Startup'
[2025.06.14-08.15.37:122][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.06.14-08.15.37:122][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.094 seconds
[2025.06.14-08.15.37:123][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.06.14-08.15.37:129][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.01 seconds.
[2025.06.14-08.15.37:129][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.07ms. RandomReadSpeed=1192.82MBs, RandomWriteSpeed=243.40MBs. Assigned SpeedClass 'Local'
[2025.06.14-08.15.37:130][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.06.14-08.15.37:130][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.06.14-08.15.37:130][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.06.14-08.15.37:130][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.06.14-08.15.37:130][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.06.14-08.15.37:130][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.06.14-08.15.37:130][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.06.14-08.15.37:131][  0]LogShaderCompilers: Guid format shader working directory is 33 characters bigger than the processId version (H:/P4/dev/Baoli/Intermediate/Shaders/WorkingDirectory/46348/).
[2025.06.14-08.15.37:131][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/DF8F06684D90698F7A2B4CBDDB5666BA/'.
[2025.06.14-08.15.37:131][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.06.14-08.15.37:131][  0]LogShaderCompilers: Display: Using Local Shader Compiler with 16 workers.
[2025.06.14-08.15.37:132][  0]LogShaderCompilers: Display: Compiling shader autogen file: H:/P4/dev/Baoli/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
[2025.06.14-08.15.37:132][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.06.14-08.15.37:621][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.06.14-08.15.38:173][  0]LogSlate: Using FreeType 2.10.0
[2025.06.14-08.15.38:173][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.06.14-08.15.38:173][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.14-08.15.38:173][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.14-08.15.38:175][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.14-08.15.38:175][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.14-08.15.38:175][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.14-08.15.38:175][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.14-08.15.38:197][  0]LogAssetRegistry: FAssetRegistry took 0.0023 seconds to start up
[2025.06.14-08.15.38:199][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.06.14-08.15.38:203][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.001s loading caches H:/P4/dev/Baoli/Intermediate/CachedAssetRegistry_*.bin.
[2025.06.14-08.15.38:370][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.06.14-08.15.38:372][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.06.14-08.15.38:372][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.06.14-08.15.38:372][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.06.14-08.15.38:382][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.06.14-08.15.38:382][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.06.14-08.15.38:405][  0]LogDeviceProfileManager: Active device profile: [0000051068778C00][000005105F256000 66] WindowsEditor
[2025.06.14-08.15.38:406][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.06.14-08.15.38:406][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.22621.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Sdk_HasBestVersion")
[2025.06.14-08.15.38:409][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="H:/P4/dev/Baoli/Baoli.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/P4/dev/Baoli/Intermediate/TurnkeyReport_1.log" -log="H:/P4/dev/Baoli/Intermediate/TurnkeyLog_1.log" -project="H:/P4/dev/Baoli/Baoli.uproject"  -Device=Win64@DESKTOP-E41IK6R'
[2025.06.14-08.15.38:409][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/P4/dev/Baoli/Baoli.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/P4/dev/Baoli/Intermediate/TurnkeyReport_1.log" -log="H:/P4/dev/Baoli/Intermediate/TurnkeyLog_1.log" -project="H:/P4/dev/Baoli/Baoli.uproject"  -Device=Win64@DESKTOP-E41IK6R" -nocompile -nocompileuat ]
[2025.06.14-08.15.38:436][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-08.15.38:436][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 18 to allow recursive sync load to finish
[2025.06.14-08.15.38:436][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.14-08.15.38:437][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-08.15.38:437][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 19 to allow recursive sync load to finish
[2025.06.14-08.15.38:437][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.14-08.15.38:437][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-08.15.38:438][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 20 to allow recursive sync load to finish
[2025.06.14-08.15.38:438][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.14-08.15.38:438][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-08.15.38:438][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 21 to allow recursive sync load to finish
[2025.06.14-08.15.38:438][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.14-08.15.38:438][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-08.15.38:438][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-08.15.38:438][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 23 to allow recursive sync load to finish
[2025.06.14-08.15.38:438][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.14-08.15.38:439][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-08.15.38:439][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 24 to allow recursive sync load to finish
[2025.06.14-08.15.38:439][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.14-08.15.38:439][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-08.15.38:439][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-08.15.38:439][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 26 to allow recursive sync load to finish
[2025.06.14-08.15.38:439][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.14-08.15.38:439][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-08.15.38:439][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 27 to allow recursive sync load to finish
[2025.06.14-08.15.38:439][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.14-08.15.38:439][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-08.15.38:439][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDiffuse with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.14-08.15.38:439][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-08.15.38:439][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 29 to allow recursive sync load to finish
[2025.06.14-08.15.38:439][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.06.14-08.15.38:439][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-08.15.38:440][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 30 to allow recursive sync load to finish
[2025.06.14-08.15.38:440][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.06.14-08.15.38:440][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.14-08.15.38:441][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 28 to allow recursive sync load to finish
[2025.06.14-08.15.38:441][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.14-08.15.38:441][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-08.15.38:441][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 31 to allow recursive sync load to finish
[2025.06.14-08.15.38:441][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.14-08.15.38:441][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 25 to allow recursive sync load to finish
[2025.06.14-08.15.38:441][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.14-08.15.38:441][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-08.15.38:441][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 32 to allow recursive sync load to finish
[2025.06.14-08.15.38:441][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.14-08.15.38:441][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 22 to allow recursive sync load to finish
[2025.06.14-08.15.38:441][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.14-08.15.38:441][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-08.15.38:441][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 33 to allow recursive sync load to finish
[2025.06.14-08.15.38:441][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.14-08.15.38:603][  0]LogMeshReduction: Display: Mesh reduction module (r.MeshReductionModule) set to "InstaLODMeshReduction" which doesn't exist.
[2025.06.14-08.15.38:603][  0]LogMeshReduction: Display: Skeletal mesh reduction module (r.SkeletalMeshReductionModule) set to "InstaLODMeshReduction" which doesn't exist.
[2025.06.14-08.15.38:603][  0]LogMeshReduction: Display: HLOD mesh reduction module (r.ProxyLODMeshReductionModule) set to "InstaLODMeshReduction" which doesn't exist.
[2025.06.14-08.15.38:615][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.06.14-08.15.38:615][  0]LogMeshReduction: Display: Using InstaLODMeshReduction for automatic skeletal mesh reduction
[2025.06.14-08.15.38:615][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.06.14-08.15.38:615][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.06.14-08.15.38:615][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.06.14-08.15.38:738][  0]LogConfig: Branch 'PIEPreviewSettings' had been unloaded. Reloading on-demand took 0.89ms
[2025.06.14-08.15.38:759][  0]LogConfig: Branch 'GameplayTagsList' had been unloaded. Reloading on-demand took 0.45ms
[2025.06.14-08.15.38:770][  0]LogConfig: Branch 'TemplateDefs' had been unloaded. Reloading on-demand took 0.46ms
[2025.06.14-08.15.38:771][  0]LogConfig: Branch 'TemplateCategories' had been unloaded. Reloading on-demand took 0.44ms
[2025.06.14-08.15.38:965][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.06.14-08.15.38:965][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.06.14-08.15.38:970][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.06.14-08.15.38:970][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.06.14-08.15.38:971][  0]LogLiveCoding: Display: First instance in process group "UE_Baoli_0x736adef1", spawning console
[2025.06.14-08.15.38:974][  0]LogLiveCoding: Display: Waiting for server
[2025.06.14-08.15.38:990][  0]LogSlate: Border
[2025.06.14-08.15.38:990][  0]LogSlate: BreadcrumbButton
[2025.06.14-08.15.38:990][  0]LogSlate: Brushes.Title
[2025.06.14-08.15.38:990][  0]LogSlate: Default
[2025.06.14-08.15.38:990][  0]LogSlate: Icons.Save
[2025.06.14-08.15.38:990][  0]LogSlate: Icons.Toolbar.Settings
[2025.06.14-08.15.38:990][  0]LogSlate: ListView
[2025.06.14-08.15.38:990][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.06.14-08.15.38:990][  0]LogSlate: SoftwareCursor_Grab
[2025.06.14-08.15.38:990][  0]LogSlate: TableView.DarkRow
[2025.06.14-08.15.38:990][  0]LogSlate: TableView.Row
[2025.06.14-08.15.38:990][  0]LogSlate: TreeView
[2025.06.14-08.15.39:098][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.06.14-08.15.39:100][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 2.369 ms
[2025.06.14-08.15.39:133][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.06.14-08.15.39:134][  0]LogConfig: Branch 'Mass' had been unloaded. Reloading on-demand took 0.46ms
[2025.06.14-08.15.39:163][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.06.14-08.15.39:163][  0]LogInit: XR: MultiViewport is Disabled
[2025.06.14-08.15.39:163][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.06.14-08.15.39:163][  0]LogTurnkeySupport: Turnkey Device: Win64@DESKTOP-E41IK6R: (Name=DESKTOP-E41IK6R, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.22631.0, Flags="Device_InstallSoftwareValid")
[2025.06.14-08.15.39:425][  0]LogConfig: Branch 'TranslationPickerSettings' had been unloaded. Reloading on-demand took 0.45ms
[2025.06.14-08.15.39:607][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.06.14-08.15.39:612][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.06.14-08.15.39:612][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.06.14-08.15.39:612][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:61945'.
[2025.06.14-08.15.39:614][  0]LogUdpMessaging: Display: Added local interface '172.17.160.1' to multicast group '230.0.0.1:6666'
[2025.06.14-08.15.39:614][  0]LogUdpMessaging: Display: Added local interface '192.168.31.37' to multicast group '230.0.0.1:6666'
[2025.06.14-08.15.39:614][  0]LogUdpMessaging: Display: Added local interface '172.17.64.1' to multicast group '230.0.0.1:6666'
[2025.06.14-08.15.39:652][  0]LogNNERuntimeORT: Failed to create D3D12 device, D3D12CreateDevice error code :8007000e
[2025.06.14-08.15.39:652][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.06.14-08.15.39:652][  0]LogNNERuntimeORT: 0: NVIDIA GeForce RTX 2080 Ti (Compute, Graphics)
[2025.06.14-08.15.39:652][  0]LogNNERuntimeORT: 1: AMD Radeon RX 6900 XT (Compute, Graphics)
[2025.06.14-08.15.39:652][  0]LogNNERuntimeORT: 2: Microsoft Basic Render Driver (Compute, Graphics)
[2025.06.14-08.15.39:652][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.06.14-08.15.39:831][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.06.14-08.15.39:831][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.06.14-08.15.39:846][  0]LogMetaSound: MetaSound Engine Initialized
[2025.06.14-08.15.39:988][  0]LogWebBrowser: Loaded CEF3 version 90.6.7.2358 from D:/UE_5.5/Engine/Binaries/ThirdParty/CEF3/Win64
[2025.06.14-08.15.39:988][  0]LogCEFBrowser: CEF GPU acceleration enabled
[2025.06.14-08.15.40:173][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: FE165F08193042BD8000000000007200 | Instance: E217D7CA43FF72AA4A5BB4B349FA40CE (DESKTOP-E41IK6R-46348).
[2025.06.14-08.15.40:252][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.06.14-08.15.40:497][  0]LogTemp: Warning: ✓ AI Perception system enabled and events bound
[2025.06.14-08.15.40:577][  0]LogConfig: Applying CVar settings from Section [/Script/PCG.PCGEngineSettings] File [Engine]
[2025.06.14-08.15.40:880][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.06.14-08.15.40:890][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.06.14-08.15.40:906][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.06.14-08.15.40:906][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.06.14-08.15.41:004][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/glf/resources/plugInfo.json'
[2025.06.14-08.15.41:004][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hio/resources/plugInfo.json'
[2025.06.14-08.15.41:004][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdAbc/resources/plugInfo.json'
[2025.06.14-08.15.41:004][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProcImaging/resources/plugInfo.json'
[2025.06.14-08.15.41:005][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImaging/resources/plugInfo.json'
[2025.06.14-08.15.41:005][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hd/resources/plugInfo.json'
[2025.06.14-08.15.41:005][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ndr/resources/plugInfo.json'
[2025.06.14-08.15.41:005][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkel/resources/plugInfo.json'
[2025.06.14-08.15.41:005][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRi/resources/plugInfo.json'
[2025.06.14-08.15.41:006][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdLux/resources/plugInfo.json'
[2025.06.14-08.15.41:007][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkelImaging/resources/plugInfo.json'
[2025.06.14-08.15.41:007][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProc/resources/plugInfo.json'
[2025.06.14-08.15.41:007][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdrGlslfx/resources/plugInfo.json'
[2025.06.14-08.15.41:007][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/plugInfo.json'
[2025.06.14-08.15.41:008][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdStorm/resources/plugInfo.json'
[2025.06.14-08.15.41:008][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImagingGL/resources/plugInfo.json'
[2025.06.14-08.15.41:008][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdSt/resources/plugInfo.json'
[2025.06.14-08.15.41:009][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRender/resources/plugInfo.json'
[2025.06.14-08.15.41:009][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hgiGL/resources/plugInfo.json'
[2025.06.14-08.15.41:009][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdHydra/resources/plugInfo.json'
[2025.06.14-08.15.41:009][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShade/resources/plugInfo.json'
[2025.06.14-08.15.41:009][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdPhysics/resources/plugInfo.json'
[2025.06.14-08.15.41:010][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVolImaging/resources/plugInfo.json'
[2025.06.14-08.15.41:010][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdUI/resources/plugInfo.json'
[2025.06.14-08.15.41:011][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMtlx/resources/plugInfo.json'
[2025.06.14-08.15.41:011][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ar/resources/plugInfo.json'
[2025.06.14-08.15.41:011][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRiPxrImaging/resources/plugInfo.json'
[2025.06.14-08.15.41:011][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVol/resources/plugInfo.json'
[2025.06.14-08.15.41:012][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdf/resources/plugInfo.json'
[2025.06.14-08.15.41:012][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdGeom/resources/plugInfo.json'
[2025.06.14-08.15.41:012][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdGp/resources/plugInfo.json'
[2025.06.14-08.15.41:013][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMedia/resources/plugInfo.json'
[2025.06.14-08.15.41:013][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShaders/resources/plugInfo.json'
[2025.06.14-08.15.41:013][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdx/resources/plugInfo.json'
[2025.06.14-08.15.41:013][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/codegenTemplates/plugInfo.json'
[2025.06.14-08.15.41:118][  0]LogTimingProfiler: Initialize
[2025.06.14-08.15.41:118][  0]LogTimingProfiler: OnSessionChanged
[2025.06.14-08.15.41:118][  0]LoadingProfiler: Initialize
[2025.06.14-08.15.41:118][  0]LoadingProfiler: OnSessionChanged
[2025.06.14-08.15.41:118][  0]LogNetworkingProfiler: Initialize
[2025.06.14-08.15.41:118][  0]LogNetworkingProfiler: OnSessionChanged
[2025.06.14-08.15.41:118][  0]LogMemoryProfiler: Initialize
[2025.06.14-08.15.41:118][  0]LogMemoryProfiler: OnSessionChanged
[2025.06.14-08.15.41:235][  0]LogConfig: Branch 'ObjectMixerSerializedData' had been unloaded. Reloading on-demand took 0.45ms
[2025.06.14-08.15.41:258][  0]LogConfig: Branch 'Crypto' had been unloaded. Reloading on-demand took 0.42ms
[2025.06.14-08.15.41:595][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.14-08.15.41:595][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.14-08.15.41:664][  0]LogCollectionManager: Loaded 1 collections in 0.000710 seconds
[2025.06.14-08.15.41:666][  0]LogFileCache: Scanning file cache for directory 'H:/P4/dev/Baoli/Saved/Collections/' took 0.00s
[2025.06.14-08.15.41:668][  0]LogFileCache: Scanning file cache for directory 'H:/P4/dev/Baoli/Content/Developers/Shashank/Collections/' took 0.00s
[2025.06.14-08.15.41:670][  0]LogFileCache: Scanning file cache for directory 'H:/P4/dev/Baoli/Content/Collections/' took 0.00s
[2025.06.14-08.15.41:726][  0]LogConfig: Branch 'Plugins' had been unloaded. Reloading on-demand took 0.46ms
[2025.06.14-08.15.41:728][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.14-08.15.41:728][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.14-08.15.41:730][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.14-08.15.41:730][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.14-08.15.41:730][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.14-08.15.41:730][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.14-08.15.41:756][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.14-08.15.41:756][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.14-08.15.41:789][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.14-08.15.41:789][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.14-08.15.41:789][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.14-08.15.41:789][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.14-08.15.41:789][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.14-08.15.41:789][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.14-08.15.41:816][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.14-08.15.41:816][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.14-08.15.41:833][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.17.0-39599718 booting at 2025-06-14T08:15:41.833Z using C
[2025.06.14-08.15.41:833][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.22621.4391.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=Baoli, ProductVersion=++UE5+Release-5.5-***********, IsServer=false, Flags=DisableOverlay]
[2025.06.14-08.15.41:833][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.06.14-08.15.41:834][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.06.14-08.15.41:838][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.06.14-08.15.41:838][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.06.14-08.15.41:838][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.06.14-08.15.41:838][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.000049
[2025.06.14-08.15.41:838][  0]LogFab: Display: Logging in using persist
[2025.06.14-08.15.41:840][  0]LogEOSSDK: Warning: LogEOSAuth: No existing persistent auth credentials were found for automatic login.
[2025.06.14-08.15.41:883][  0]LogUObjectArray: 47648 objects as part of root set at end of initial load.
[2025.06.14-08.15.41:884][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.06.14-08.15.41:895][  0]LogStreaming: Display: AsyncLoading2 - NotifyRegistrationComplete: Registered 40252 public script object entries (1076.97 KB)
[2025.06.14-08.15.41:896][  0]LogStreaming: Display: AsyncLoading2 - Thread Started: false, IsInitialLoad: false
[2025.06.14-08.15.42:002][  0]LogEngine: Initializing Engine...
[2025.06.14-08.15.42:003][  0]LogGameFeatures: Initializing game features subsystem
[2025.06.14-08.15.42:003][  0]InkPlusPlus: FStory::FStory 000005107C116010
[2025.06.14-08.15.42:003][  0]InkPlusPlus: Warning: WARNING: Version of ink used to build story doesn't match current version of engine. Non-critical, but recommend synchronising.
[2025.06.14-08.15.42:006][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.06.14-08.15.42:006][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.06.14-08.15.42:101][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.06.14-08.15.42:113][  0]LogGameFeatures: Scanning for built-in game feature plugins
[2025.06.14-08.15.42:113][  0]LogGameFeatures: Loading 233 builtins
[2025.06.14-08.15.42:115][  0]LogGameFeatures: Display: Total built in plugin load time 0.0008s
[2025.06.14-08.15.42:115][  0]LogStats: BuiltInGameFeaturePlugins loaded. -  0.001 s
[2025.06.14-08.15.42:116][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.06.14-08.15.42:138][  0]LogNetVersion: Set ProjectVersion to Alpha. Version Checksum will be recalculated on next use.
[2025.06.14-08.15.42:138][  0]LogInit: Texture streaming: Enabled
[2025.06.14-08.15.42:146][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.5.4-40574608+++UE5+Release-5.5
[2025.06.14-08.15.42:152][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.06.14-08.15.42:158][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.06.14-08.15.42:158][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.06.14-08.15.42:158][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.06.14-08.15.42:158][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.06.14-08.15.42:158][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.14-08.15.42:158][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.14-08.15.42:158][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.14-08.15.42:158][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.14-08.15.42:158][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.14-08.15.42:158][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.14-08.15.42:158][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.14-08.15.42:158][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.14-08.15.42:158][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.14-08.15.42:158][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.14-08.15.42:158][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.14-08.15.42:162][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.14-08.15.42:223][  0]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.14-08.15.42:223][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.14-08.15.42:223][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.14-08.15.42:223][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.14-08.15.42:225][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.06.14-08.15.42:225][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.06.14-08.15.42:226][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.06.14-08.15.42:226][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.06.14-08.15.42:226][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.06.14-08.15.42:226][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.06.14-08.15.42:226][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.06.14-08.15.42:233][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.06.14-08.15.42:235][  0]LogInit: Undo buffer set to 256 MB
[2025.06.14-08.15.42:235][  0]LogInit: Transaction tracking system initialized
[2025.06.14-08.15.42:295][  0]LogConfig: Branch 'LocalizationServiceSettings' had been unloaded. Reloading on-demand took 0.50ms
[2025.06.14-08.15.42:296][  0]LocalizationService: Localization service is disabled
[2025.06.14-08.15.42:502][  0]LogPython: Using Python 3.11.8
[2025.06.14-08.15.42:633][  0]LogFileCache: Scanning file cache for directory 'H:/P4/dev/Baoli/Content/' took 0.18s
[2025.06.14-08.15.43:562][  0]LogPython: Display: No enabled plugins with python dependencies found, skipping
[2025.06.14-08.15.43:581][  0]LogRenderer: Requested compilation of Path Tracing RTPSOs (0 permutations).
[2025.06.14-08.15.43:658][  0]LogStreaming: Warning: Failed to read file 'Common/Selector.png' error.
[2025.06.14-08.15.43:658][  0]LogSlate: Could not find file for Slate resource: Common/Selector.png
[2025.06.14-08.15.43:691][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.06.14-08.15.43:709][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.14-08.15.43:709][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.14-08.15.43:711][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.14-08.15.43:711][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.14-08.15.43:711][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.14-08.15.43:711][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.14-08.15.43:737][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.14-08.15.43:737][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.14-08.15.43:741][  0]LogStreaming: Warning: Failed to read file 'Common/Selector.png' error.
[2025.06.14-08.15.43:741][  0]LogSlate: Could not find file for Slate resource: Common/Selector.png
[2025.06.14-08.15.43:762][  0]LogEditorDataStorage: Initializing
[2025.06.14-08.15.43:763][  0]LogEditorDataStorage: Initialized
[2025.06.14-08.15.43:770][  0]LogCore: Display: GameplayInsights module auto-connecting to local trace server...
[2025.06.14-08.15.43:787][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/Performances/MHP_Scene1_01.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/MHI.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_9/MySlate_9_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_7/MySlate_7_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_9/MySlate_9_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/Performance/MHP_Baoli.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_7/MySlate_7_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_6/MySlate_6_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_6/MySlate_6_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_5/MySlate_5_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_5/MySlate_5_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_4/MySlate_4_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_35/MySlate_35_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_34/MySlate_34_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_34/MySlate_34_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_4/MySlate_4_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_35/MySlate_35_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_36/MySlate_36_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_32/MySlate_32_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_32/MySlate_32_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_33/MySlate_33_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_31/MySlate_31_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_31/MySlate_31_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_33/MySlate_33_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_30/MySlate_30_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_30/MySlate_30_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_29/MySlate_29_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_36/MySlate_36_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_26/MySlate_26_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_25/MySlate_25_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_25/MySlate_25_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_28/MySlate_28_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_28/MySlate_28_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_27/MySlate_27_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_27/MySlate_27_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_23/MySlate_23_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_29/MySlate_29_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_26/MySlate_26_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_24/MySlate_24_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:848][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_23/MySlate_23_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:849][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_22/MySlate_22_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:849][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_22/MySlate_22_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:849][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_24/MySlate_24_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:849][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_20/MySlate_20_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:849][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_21/MySlate_21_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:849][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_20/MySlate_20_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:849][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_21/MySlate_21_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:849][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_19/MySlate_19_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:849][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_19/MySlate_19_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:849][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_17/MySlate_17_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:849][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_14/MySlate_14_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:849][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_18/MySlate_18_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:849][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_18/MySlate_18_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:849][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_17/MySlate_17_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:849][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_13/MySlate_13_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:849][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_15/MySlate_15_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:849][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_13/MySlate_13_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:849][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_16/MySlate_16_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:849][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_12/MySlate_12_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:849][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_11/MySlate_11_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:849][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_14/MySlate_14_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:849][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_16/MySlate_16_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:849][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_11/MySlate_11_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:849][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_12/MySlate_12_Calibration_Depth_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:849][  0]LogAssetRegistry: Display: Package H:/P4/dev/Baoli/Content/LiveLinkMocap/CS_iPhone_Ingested/20250212_MySlate_15/MySlate_15_Calibration_RGB_LensFile.uasset uses an unknown custom version and cannot be loaded for the AssetRegistry
[2025.06.14-08.15.43:849][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.06.14-08.15.44:379][  0]LogSlate: Took 0.000132 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.06.14-08.15.45:002][  0]LogAssetRegistry: Display: Asset registry cache written as 106.4 MiB to H:/P4/dev/Baoli/Intermediate/CachedAssetRegistry_*.bin
[2025.06.14-08.15.47:350][  0]LogSlate: Window 'Restore Packages' being destroyed
[2025.06.14-08.15.47:373][  0]LogUnrealEdMisc: Loading editor; pre map load, took 13.193
[2025.06.14-08.15.47:374][  0]Cmd: MAP LOAD FILE="H:/P4/dev/Baoli/Content/Levels/DefaultLevel.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=4
[2025.06.14-08.15.47:376][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.06.14-08.15.47:376][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.14-08.15.47:386][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.14-08.15.47:388][  0]InkPlusPlus: FStory::~FStory 000005107C116010
[2025.06.14-08.15.47:388][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.52ms
[2025.06.14-08.15.47:449][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Body (0xD40953E846A84DE2) /Game/Assets/Kettle/Body (0xD40953E846A84DE2) - Skipping non mounted imported package /Game/Assets/Kettle/Black_1 (0x18AC24F8BF67EDD8)
[2025.06.14-08.15.47:450][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Body (0xD40953E846A84DE2) /Game/Assets/Kettle/Body (0xD40953E846A84DE2) - Skipping non mounted imported package /Game/Assets/Kettle/Black_Glossy (0x2CCA389A36D3E860)
[2025.06.14-08.15.47:450][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Body (0xD40953E846A84DE2) /Game/Assets/Kettle/Body (0xD40953E846A84DE2) - Skipping non mounted imported package /Game/Assets/Kettle/material (0x38FB08B605AA9364)
[2025.06.14-08.15.47:450][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Lid (0xC21C5F52A454D47B) /Game/Assets/Kettle/Lid (0xC21C5F52A454D47B) - Skipping non mounted imported package /Game/Assets/Kettle/Black_1 (0x18AC24F8BF67EDD8)
[2025.06.14-08.15.47:450][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Kettle/Lid (0xC21C5F52A454D47B) /Game/Assets/Kettle/Lid (0xC21C5F52A454D47B) - Skipping non mounted imported package /Game/Assets/Kettle/Black_Dark (0xC267FEC07D768F2)
[2025.06.14-08.15.47:492][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/TV/TVfront (0x6BB0285A3C0452DE) /Game/Assets/TV/TVfront (0x6BB0285A3C0452DE) - Skipping non mounted imported package /Interchange/gltf/MaterialInstances/MI_Default_Opaque (0x76C6961D9FD0C56A)
[2025.06.14-08.15.47:493][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/TV/TVback (0x8B181E584DB8A471) /Game/Assets/TV/TVback (0x8B181E584DB8A471) - Skipping non mounted imported package /Interchange/gltf/MaterialInstances/MI_Default_Opaque (0x76C6961D9FD0C56A)
[2025.06.14-08.15.47:705][  0]LogStreaming: Display: ImportPackages: SkipPackage: /Game/Assets/Washingmachine/steel (0x8E7AA179EA3A2994) /Game/Assets/Washingmachine/steel (0x8E7AA179EA3A2994) - Skipping non mounted imported package /Interchange/gltf/MaterialInstances/MI_Default_Opaque_DS (0xF21345B7066A3DF7)
[2025.06.14-08.15.48:187][  0]LogLinker: Warning: [AssetLog] H:\P4\dev\Baoli\Content\BaoliAssets\BrickInstances\Brick_low_001.uasset: VerifyImport: Failed to find script package for import object 'Package /Script/rdInst'
[2025.06.14-08.15.50:013][  0]LogEditorDomain: Display: Class /Script/rdInst.rdInstAssetUserData is imported by a package but does not exist in memory. EditorDomain keys for packages using it will be invalid if it still exists.
	To clear this message, resave packages that use the deleted class, or load its module earlier than the packages that use it are referenced.
[2025.06.14-08.15.51:172][  0]LogStreaming: Display: Flushing package /Game/MetaHumans/Common/Common/IK_metahuman (state: WaitingForIo) recursively from another package /Game/MetaHumans/Common/Common/RTG_metahuman_base_skel_AnimBP (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-08.15.51:175][  0]LogStreaming: Display: Package /Game/MetaHumans/Common/Common/IK_metahuman has reached state ExportsDone > CreateLinkerLoadExports, releasing request 334 to allow recursive sync load to finish
[2025.06.14-08.15.51:176][  0]LogStreaming: Display: Merging postload groups of package /Game/MetaHumans/Common/Common/IK_metahuman with requester package /Game/MetaHumans/Common/Common/RTG_metahuman_base_skel_AnimBP
[2025.06.14-08.15.51:176][  0]LogStreaming: Display: Flushing package /Game/MetaHumans/Common/Common/IK_metahuman (state: ExportsDone) recursively from another package /Game/MetaHumans/Common/Common/RTG_metahuman_base_skel_AnimBP (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-08.15.51:176][  0]LogStreaming: Display: Package /Game/MetaHumans/Common/Common/IK_metahuman has reached state ExportsDone > CreateLinkerLoadExports, releasing request 335 to allow recursive sync load to finish
[2025.06.14-08.15.51:176][  0]LogStreaming: Display: Merging postload groups of package /Game/MetaHumans/Common/Common/IK_metahuman with requester package /Game/MetaHumans/Common/Common/RTG_metahuman_base_skel_AnimBP
[2025.06.14-08.15.51:779][  0]LogBlueprint: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Blueprints\CBP_SandboxCharacter.uasset: [Compiler] Input pin  Debug Session Unique Identifier  specifying non-default value no longer exists on node  Motion Match . Please refresh node or reset pin to default value to remove pin.
[2025.06.14-08.15.52:926][  0]LogStreaming: Display: Flushing package /Engine/EditorResources/S_Pawn (state: WaitingForIo) recursively from another package /Game/Levels/DefaultLevel (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-08.15.52:926][  0]LogStreaming: Display: Package /Engine/EditorResources/S_Pawn has reached state ExportsDone > CreateLinkerLoadExports, releasing request 336 to allow recursive sync load to finish
[2025.06.14-08.15.52:926][  0]LogStreaming: Display: Merging postload groups of package /Engine/EditorResources/S_Pawn with requester package /Game/Levels/DefaultLevel
[2025.06.14-08.15.52:926][  0]LogStreaming: Display: Flushing package /Engine/EditorResources/S_Pawn (state: ExportsDone) recursively from another package /Game/Levels/DefaultLevel (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.14-08.15.52:926][  0]LogStreaming: Display: Package /Engine/EditorResources/S_Pawn has reached state ExportsDone > CreateLinkerLoadExports, releasing request 337 to allow recursive sync load to finish
[2025.06.14-08.15.52:926][  0]LogStreaming: Display: Merging postload groups of package /Engine/EditorResources/S_Pawn with requester package /Game/Levels/DefaultLevel
[2025.06.14-08.15.53:036][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound.MSS_FoleySound' successfully migrated editor data in target document version 'v1.12'.
[2025.06.14-08.15.53:037][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Handplant' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Handplant.MSS_FoleySound_Handplant' successfully migrated editor data in target document version 'v1.12'.
[2025.06.14-08.15.53:038][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Jump' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Jump.MSS_FoleySound_Jump' successfully migrated editor data in target document version 'v1.12'.
[2025.06.14-08.15.53:038][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Land' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Land.MSS_FoleySound_Land' successfully migrated editor data in target document version 'v1.12'.
[2025.06.14-08.15.53:040][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Run' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Run.MSS_FoleySound_Run' successfully migrated editor data in target document version 'v1.12'.
[2025.06.14-08.15.53:041][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_RunBackwards' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_RunBackwards.MSS_FoleySound_RunBackwards' successfully migrated editor data in target document version 'v1.12'.
[2025.06.14-08.15.53:042][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_RunStrafe' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_RunStrafe.MSS_FoleySound_RunStrafe' successfully migrated editor data in target document version 'v1.12'.
[2025.06.14-08.15.53:043][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Scuff' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Scuff.MSS_FoleySound_Scuff' successfully migrated editor data in target document version 'v1.12'.
[2025.06.14-08.15.53:044][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_ScuffPivot' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_ScuffPivot.MSS_FoleySound_ScuffPivot' successfully migrated editor data in target document version 'v1.12'.
[2025.06.14-08.15.53:045][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_ScuffWall' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_ScuffWall.MSS_FoleySound_ScuffWall' successfully migrated editor data in target document version 'v1.12'.
[2025.06.14-08.15.53:047][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Tumble' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Tumble.MSS_FoleySound_Tumble' successfully migrated editor data in target document version 'v1.12'.
[2025.06.14-08.15.53:048][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_Walk' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_Walk.MSS_FoleySound_Walk' successfully migrated editor data in target document version 'v1.12'.
[2025.06.14-08.15.53:048][  0]LogMetaSound: Display: Resave recommended: Asset 'MSS_FoleySound_WalkBackwards' at '/Game/Audio/Foley/MetaSounds/MSS_FoleySound_WalkBackwards.MSS_FoleySound_WalkBackwards' successfully migrated editor data in target document version 'v1.12'.
[2025.06.14-08.15.54:670][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.8.dll
[2025.06.14-08.15.54:704][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.10.dll
[2025.06.14-08.15.54:719][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm_High...
[2025.06.14-08.15.54:719][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_jeans_nrm_High...
[2025.06.14-08.15.55:122][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_High.m_med_nrw_btm_jeans_nrm_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.14-08.15.55:124][  0]LogSkeletalMesh: Built Skeletal Mesh [0.41s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_High.m_med_nrw_btm_jeans_nrm_High
[2025.06.14-08.15.55:134][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.7.dll
[2025.06.14-08.15.55:719][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_cargopants_High...
[2025.06.14-08.15.56:070][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Cargopants/m_med_nrw_btm_cargopants_High.m_med_nrw_btm_cargopants_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.14-08.15.56:072][  0]LogSkeletalMesh: Built Skeletal Mesh [0.36s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Cargopants/m_med_nrw_btm_cargopants_High.m_med_nrw_btm_cargopants_High
[2025.06.14-08.15.56:179][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_High.m_med_nrw_top_crewneckt_nrm_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.14-08.15.56:182][  0]LogSkeletalMesh: Built Skeletal Mesh [1.46s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_High.m_med_nrw_top_crewneckt_nrm_High
[2025.06.14-08.15.56:491][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_runningshoes_High...
[2025.06.14-08.15.56:508][  0]LogMaterial: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Assets\Tea_Ingredients\Tea\vray_Cap_01_Mat.uasset: Failed to compile Material for platform PCD3D_SM6, Default Material will be used in game.
	(Node TextureObject) Texture Object> Requires valid texture
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D

[2025.06.14-08.15.56:509][  0]LogMaterial: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Assets\Tea_Ingredients\Tea\vray_CommonHazelNut_Mat.uasset: Failed to compile Material for platform PCD3D_SM6, Default Material will be used in game.
	(Node TextureObject) Texture Object> Requires valid texture
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D

[2025.06.14-08.15.56:510][  0]LogMaterial: Warning: [AssetLog] H:\P4\dev\Baoli\Content\Assets\Tea_Ingredients\Tea\vray_CommonHazelLeaf_Mat.uasset: Failed to compile Material for platform PCD3D_SM6, Default Material will be used in game.
	(Node TextureObject) Texture Object> Requires valid texture
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D
	(Node TextureSampleParameter2D) Param2D> Found NULL, requires Texture2D

[2025.06.14-08.15.56:860][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_High.m_med_nrw_shs_runningshoes_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.14-08.15.56:863][  0]LogSkeletalMesh: Built Skeletal Mesh [0.37s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_High.m_med_nrw_shs_runningshoes_High
[2025.06.14-08.15.58:325][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_hoodie_nrm_High...
[2025.06.14-08.15.58:763][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Hoodie/Meshes/m_med_nrw_top_hoodie_nrm_High.m_med_nrw_top_hoodie_nrm_High]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.14-08.15.58:766][  0]LogSkeletalMesh: Built Skeletal Mesh [0.44s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Hoodie/Meshes/m_med_nrw_top_hoodie_nrm_High.m_med_nrw_top_hoodie_nrm_High
[2025.06.14-08.15.58:829][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Foley.SM_Foley.
[2025.06.14-08.15.58:829][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Final.SM_Final.
[2025.06.14-08.15.58:829][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Ambient.SM_Ambient.
[2025.06.14-08.15.58:829][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Final.SM_Final.
[2025.06.14-08.15.58:829][  0]LogAudioMixer: Display: Registering submix SoundSubmix /Game/Audio/Mix/SM_Reverb.SM_Reverb.
[2025.06.14-08.15.59:562][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Jumps
[2025.06.14-08.15.59:562][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Idle_Lands_Light
[2025.06.14-08.15.59:562][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Idles
[2025.06.14-08.15.59:562][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Lands_Light
[2025.06.14-08.15.59:562][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Loops
[2025.06.14-08.15.59:562][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Pivots
[2025.06.14-08.15.59:562][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Starts
[2025.06.14-08.15.59:562][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Stops
[2025.06.14-08.15.59:562][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Lands_Light
[2025.06.14-08.15.59:562][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Loops
[2025.06.14-08.15.59:562][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Pivots
[2025.06.14-08.15.59:562][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Starts
[2025.06.14-08.15.59:562][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_FromTraversal
[2025.06.14-08.15.59:562][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_TurnInPlace
[2025.06.14-08.15.59:562][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Stops
[2025.06.14-08.15.59:562][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Starts
[2025.06.14-08.15.59:562][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Pivots
[2025.06.14-08.15.59:562][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Loops
[2025.06.14-08.15.59:562][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Walk_Lands_Light
[2025.06.14-08.15.59:562][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_TurnInPlace
[2025.06.14-08.15.59:562][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Stops
[2025.06.14-08.15.59:563][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Starts
[2025.06.14-08.15.59:564][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Pivots
[2025.06.14-08.15.59:564][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Loops
[2025.06.14-08.15.59:564][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_Lands_Light
[2025.06.14-08.15.59:564][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Run_FromTraversal
[2025.06.14-08.15.59:564][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Idles
[2025.06.14-08.15.59:564][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Dense_Stand_Idle_Lands_Light
[2025.06.14-08.15.59:708][  0]LogPoseSearch: 97dd87008026ee83e9801de70d7ebdf30743107d - PSD_Dense_Jumps BeginCache
[2025.06.14-08.15.59:709][  0]LogPoseSearch: 97dd87008026ee83e9801de70d7ebdf30743107d - PSD_Dense_Jumps BuildIndex From Cache
[2025.06.14-08.15.59:721][  0]LogPoseSearch: e779484de30bcafd27d2e77ec9d2d8a7260d3670 - PSD_Dense_Jumps_Far BeginCache
[2025.06.14-08.15.59:721][  0]LogPoseSearch: e779484de30bcafd27d2e77ec9d2d8a7260d3670 - PSD_Dense_Jumps_Far BuildIndex From Cache
[2025.06.14-08.15.59:724][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Loops
[2025.06.14-08.15.59:724][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Pivots
[2025.06.14-08.15.59:724][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Starts
[2025.06.14-08.15.59:724][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Stops
[2025.06.14-08.15.59:724][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Loops
[2025.06.14-08.15.59:724][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Pivots
[2025.06.14-08.15.59:724][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Starts
[2025.06.14-08.15.59:724][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Stops
[2025.06.14-08.15.59:724][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Starts
[2025.06.14-08.15.59:724][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Pivots
[2025.06.14-08.15.59:724][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Walk_Loops
[2025.06.14-08.15.59:725][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Stops
[2025.06.14-08.15.59:725][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Starts
[2025.06.14-08.15.59:725][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Pivots
[2025.06.14-08.15.59:725][  0]LogPoseSearch: Delaying DDC until dependents post load  - PSD_Sparse_Stand_Run_Loops
[2025.06.14-08.15.59:778][  0]LogPoseSearch: 00a2b080d1816aee86768c5e2fa73e7e23b03093 - PSD_Dense_Stand_Idle_Lands_Heavy BeginCache
[2025.06.14-08.15.59:778][  0]LogPoseSearch: 00a2b080d1816aee86768c5e2fa73e7e23b03093 - PSD_Dense_Stand_Idle_Lands_Heavy BuildIndex From Cache
[2025.06.14-08.15.59:798][  0]LogPoseSearch: de14cb19a4d494ff43b9836dd44b4a721af3b322 - PSD_Dense_Stand_Run_Lands_Heavy BeginCache
[2025.06.14-08.15.59:799][  0]LogPoseSearch: de14cb19a4d494ff43b9836dd44b4a721af3b322 - PSD_Dense_Stand_Run_Lands_Heavy BuildIndex From Cache
[2025.06.14-08.15.59:827][  0]LogPoseSearch: fd5871232a5d49e8c33fb321da4e2e6899cd0d19 - PSD_Dense_Stand_Walk_Lands_Heavy BeginCache
[2025.06.14-08.15.59:827][  0]LogPoseSearch: fd5871232a5d49e8c33fb321da4e2e6899cd0d19 - PSD_Dense_Stand_Walk_Lands_Heavy BuildIndex From Cache
[2025.06.14-08.15.59:941][  0]LogPoseSearch: b9ad6da2b4015614aa861a663ab0dc2c7376fcdd - PSD_Traversal BeginCache
[2025.06.14-08.15.59:941][  0]LogPoseSearch: b9ad6da2b4015614aa861a663ab0dc2c7376fcdd - PSD_Traversal BuildIndex From Cache
[2025.06.14-08.15.59:951][  0]LogPoseSearch: 7c05dccd4ed9ffbd1aa4765f1bb5c5f8a4946afb - PSD_Dense_Jumps_FromTraversal BeginCache
[2025.06.14-08.15.59:951][  0]LogPoseSearch: 7c05dccd4ed9ffbd1aa4765f1bb5c5f8a4946afb - PSD_Dense_Jumps_FromTraversal BuildIndex From Cache
[2025.06.14-08.15.59:960][  0]LogPoseSearch: 4e0461e9846a71bf219c5af7fa774ff910bf9233 - PSD_Dense_Stand_Walk_FromTraversal BeginCache
[2025.06.14-08.15.59:960][  0]LogPoseSearch: 4e0461e9846a71bf219c5af7fa774ff910bf9233 - PSD_Dense_Stand_Walk_FromTraversal BuildIndex From Cache
[2025.06.14-08.15.59:962][  0]LogPoseSearch: fa8f245f6e288a9a1ae5bf8fa12df86bbe2069a0 - PSD_Dense_Stand_Run_SpinTransition BeginCache
[2025.06.14-08.15.59:962][  0]LogPoseSearch: fa8f245f6e288a9a1ae5bf8fa12df86bbe2069a0 - PSD_Dense_Stand_Run_SpinTransition BuildIndex From Cache
[2025.06.14-08.15.59:963][  0]LogPoseSearch: f0462c591ae871c1a37384b0990d6acc4e4d51b1 - PSD_Dense_Stand_Walk_SpinTransition BeginCache
[2025.06.14-08.15.59:963][  0]LogPoseSearch: f0462c591ae871c1a37384b0990d6acc4e4d51b1 - PSD_Dense_Stand_Walk_SpinTransition BuildIndex From Cache
[2025.06.14-08.15.59:974][  0]LogSkeletalMesh: Building Skeletal Mesh m_srt_unw_btm_shorts_nrm...
[2025.06.14-08.15.59:977][  0]LogSkeletalMesh: Building Skeletal Mesh m_srt_unw_top_crewneckt_nrm...
[2025.06.14-08.15.59:978][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm_Cinematic...
[2025.06.14-08.16.00:152][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Short/UnderWeight/Bottoms/Shorts/m_srt_unw_btm_shorts_nrm.m_srt_unw_btm_shorts_nrm]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.14-08.16.00:154][  0]LogSkeletalMesh: Built Skeletal Mesh [0.18s] /Game/MetaHumans/Common/Male/Short/UnderWeight/Bottoms/Shorts/m_srt_unw_btm_shorts_nrm.m_srt_unw_btm_shorts_nrm
[2025.06.14-08.16.00:219][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_cargopants...
[2025.06.14-08.16.00:253][  0]LogPoseSearch: af9e19fa6af4f79379007c0ce63cd543e2a68bcc - PSD_Dense_Crouch_Idle BeginCache
[2025.06.14-08.16.00:253][  0]LogPoseSearch: af9e19fa6af4f79379007c0ce63cd543e2a68bcc - PSD_Dense_Crouch_Idle BuildIndex From Cache
[2025.06.14-08.16.00:279][  0]LogPoseSearch: e87fab46887878f88a1e636954252f6675d46fc6 - PSD_Dense_Crouch_Loop BeginCache
[2025.06.14-08.16.00:279][  0]LogPoseSearch: e87fab46887878f88a1e636954252f6675d46fc6 - PSD_Dense_Crouch_Loop BuildIndex From Cache
[2025.06.14-08.16.00:319][  0]LogPoseSearch: ba16425d0776004701de0cb772f64cb39c494537 - PSD_Dense_Crouch_Pivot BeginCache
[2025.06.14-08.16.00:321][  0]LogPoseSearch: ba16425d0776004701de0cb772f64cb39c494537 - PSD_Dense_Crouch_Pivot BuildIndex From Cache
[2025.06.14-08.16.00:360][  0]LogPoseSearch: cc211e9c34f15de1dc615cac8859cd356b1639ca - PSD_Dense_Crouch_Start BeginCache
[2025.06.14-08.16.00:361][  0]LogPoseSearch: cc211e9c34f15de1dc615cac8859cd356b1639ca - PSD_Dense_Crouch_Start BuildIndex From Cache
[2025.06.14-08.16.00:404][  0]LogPoseSearch: e4992f798d3c30031eb32ea237475e6604239ff2 - PSD_Dense_Crouch_Stops BeginCache
[2025.06.14-08.16.00:405][  0]LogPoseSearch: e4992f798d3c30031eb32ea237475e6604239ff2 - PSD_Dense_Crouch_Stops BuildIndex From Cache
[2025.06.14-08.16.00:576][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Cargopants/m_med_nrw_btm_cargopants.m_med_nrw_btm_cargopants]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.14-08.16.00:578][  0]LogSkeletalMesh: Built Skeletal Mesh [0.36s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Cargopants/m_med_nrw_btm_cargopants.m_med_nrw_btm_cargopants
[2025.06.14-08.16.00:579][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_jeans_nrm_Cinematic...
[2025.06.14-08.16.00:976][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.14-08.16.00:979][  0]LogSkeletalMesh: Built Skeletal Mesh [0.40s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic
[2025.06.14-08.16.00:981][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm...
[2025.06.14-08.16.01:012][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.6.dll
[2025.06.14-08.16.01:534][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.14-08.16.01:536][  0]LogSkeletalMesh: Built Skeletal Mesh [1.56s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic
[2025.06.14-08.16.01:538][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_runningshoes_Cinematic...
[2025.06.14-08.16.01:602][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Short/UnderWeight/Tops/Crewneckt/m_srt_unw_top_crewneckt_nrm.m_srt_unw_top_crewneckt_nrm]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.14-08.16.01:605][  0]LogSkeletalMesh: Built Skeletal Mesh [1.63s] /Game/MetaHumans/Common/Male/Short/UnderWeight/Tops/Crewneckt/m_srt_unw_top_crewneckt_nrm.m_srt_unw_top_crewneckt_nrm
[2025.06.14-08.16.01:605][  0]LogSkeletalMesh: Building Skeletal Mesh m_srt_unw_shs_flipflops...
[2025.06.14-08.16.01:688][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Short/UnderWeight/Shoes/Flipflops/m_srt_unw_shs_flipflops.m_srt_unw_shs_flipflops]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.14-08.16.01:689][  0]LogSkeletalMesh: Built Skeletal Mesh [0.08s] /Game/MetaHumans/Common/Male/Short/UnderWeight/Shoes/Flipflops/m_srt_unw_shs_flipflops.m_srt_unw_shs_flipflops
[2025.06.14-08.16.01:689][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.06.14-08.16.01:914][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.14-08.16.01:914][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.14-08.16.01:917][  0]LogSkeletalMesh: Built Skeletal Mesh [0.38s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic
[2025.06.14-08.16.01:917][  0]LogSkeletalMesh: Built Skeletal Mesh [0.23s] /Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body
[2025.06.14-08.16.01:918][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_casualsneakers...
[2025.06.14-08.16.01:918][  0]LogSkeletalMesh: Building Skeletal Mesh Baoli_MC_FaceMesh...
[2025.06.14-08.16.02:016][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'DefaultLevel'.
[2025.06.14-08.16.02:017][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.14-08.16.02:084][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (MH_Friend_FaceMesh) ...
[2025.06.14-08.16.02:177][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/CasualSneakers/m_med_nrw_shs_casualsneakers.m_med_nrw_shs_casualsneakers]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.14-08.16.02:179][  0]LogSkeletalMesh: Built Skeletal Mesh [0.26s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/CasualSneakers/m_med_nrw_shs_casualsneakers.m_med_nrw_shs_casualsneakers
[2025.06.14-08.16.02:180][  0]LogSkeletalMesh: Building Skeletal Mesh MH_Friend_FaceMesh...
[2025.06.14-08.16.02:532][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm.m_med_nrw_top_crewneckt_nrm]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.14-08.16.02:535][  0]LogSkeletalMesh: Built Skeletal Mesh [1.56s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm.m_med_nrw_top_crewneckt_nrm
[2025.06.14-08.16.02:537][  0]LogSkeletalMesh: Building Skeletal Mesh Baoli_Child2_FaceMesh...
[2025.06.14-08.16.12:935][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Baoli_MC/Face/Baoli_MC_FaceMesh.Baoli_MC_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.14-08.16.12:951][  0]LogSkeletalMesh: Built Skeletal Mesh [11.03s] /Game/MetaHumans/Baoli_MC/Face/Baoli_MC_FaceMesh.Baoli_MC_FaceMesh
[2025.06.14-08.16.12:954][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.06.14-08.16.13:130][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Kellan/Male/Medium/NormalWeight/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.14-08.16.13:132][  0]LogSkeletalMesh: Built Skeletal Mesh [0.18s] /Game/MetaHumans/Kellan/Male/Medium/NormalWeight/Body/m_med_nrw_body.m_med_nrw_body
[2025.06.14-08.16.13:133][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.06.14-08.16.13:170][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.14-08.16.13:185][  0]LogSkeletalMesh: Built Skeletal Mesh [11.00s] /Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh
[2025.06.14-08.16.13:244][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.06.14-08.16.13:264][  0]LogSkeletalMesh: Building Skeletal Mesh Kellan_FaceMesh...
[2025.06.14-08.16.13:280][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 33/36 (Baoli_Child2_FaceMesh) ...
[2025.06.14-08.16.13:339][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Baoli_Child/Face/Baoli_Child2_FaceMesh.Baoli_Child2_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.14-08.16.13:355][  0]LogSkeletalMesh: Built Skeletal Mesh [10.82s] /Game/MetaHumans/Baoli_Child/Face/Baoli_Child2_FaceMesh.Baoli_Child2_FaceMesh
[2025.06.14-08.16.13:361][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 34/36 (m_med_nrw_body) ...
[2025.06.14-08.16.13:405][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Baoli_MC/Male/Medium/NormalWeight/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.14-08.16.13:410][  0]LogSkeletalMesh: Built Skeletal Mesh [0.28s] /Game/MetaHumans/Baoli_MC/Male/Medium/NormalWeight/Body/m_med_nrw_body.m_med_nrw_body
[2025.06.14-08.16.13:411][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 35/36 (Kellan_FaceMesh) ...
[2025.06.14-08.16.13:609][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Kellan/Face/Kellan_FaceMesh.Kellan_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.14-08.16.13:615][  0]LogSkeletalMesh: Built Skeletal Mesh [0.35s] /Game/MetaHumans/Kellan/Face/Kellan_FaceMesh.Kellan_FaceMesh
[2025.06.14-08.16.13:764][  0]LogUObjectHash: Compacting FUObjectHashTables data took   2.33ms
[2025.06.14-08.16.13:766][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.06.14-08.16.13:767][  0]MapCheck: Map check complete: 0 Error(s), 0 Warning(s), took 0.089ms to complete.
[2025.06.14-08.16.13:776][  0]LogUnrealEdMisc: Total Editor Startup Time, took 39.596
[2025.06.14-08.16.13:933][  0]LogActorFactory: Loading ActorFactory Class /Script/Engine.LevelInstance
[2025.06.14-08.16.14:020][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.14-08.16.14:078][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.14-08.16.14:128][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.14-08.16.14:179][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.14-08.16.14:218][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.14-08.16.14:218][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.06.14-08.16.14:218][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.14-08.16.14:219][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.06.14-08.16.14:219][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.14-08.16.14:219][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.06.14-08.16.14:219][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.14-08.16.14:219][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.06.14-08.16.14:219][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.14-08.16.14:220][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.06.14-08.16.14:220][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.14-08.16.14:220][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.06.14-08.16.14:220][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.14-08.16.14:220][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.06.14-08.16.14:220][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.14-08.16.14:220][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.06.14-08.16.14:220][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.14-08.16.14:220][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.06.14-08.16.14:221][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.14-08.16.14:221][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.06.14-08.16.14:285][  0]LogSlate: Took 0.000128 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.06.14-08.16.14:320][  0]LogSlate: Took 0.001858 seconds to synchronously load lazily loaded font '../../../Engine/Content/Editor/Slate/Fonts/NotoColorEmoji.ttf' (7610K)
[2025.06.14-08.16.14:321][  0]LogSlate: Took 0.000688 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansFallback.ttf' (3848K)
[2025.06.14-08.16.14:499][  0]LogStall: Startup...
[2025.06.14-08.16.14:502][  0]LogStall: Startup complete.
[2025.06.14-08.16.14:523][  0]LogLoad: (Engine Initialization) Total time: 40.34 seconds
[2025.06.14-08.16.14:716][  0]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.14-08.16.14:870][  0]LogPoseSearch: 1931442baeae0018ad207ad71e3ba7fb8671c408 - PSD_Sparse_Stand_Walk_Stops BeginCache
[2025.06.14-08.16.14:871][  0]LogPoseSearch: 1931442baeae0018ad207ad71e3ba7fb8671c408 - PSD_Sparse_Stand_Walk_Stops BuildIndex From Cache
[2025.06.14-08.16.14:871][  0]LogPoseSearch: 7422884d3ff8981c6046792dc10fe09a4c24308a - PSD_Sparse_Stand_Walk_Starts BeginCache
[2025.06.14-08.16.14:873][  0]LogPoseSearch: 9b1e0fcf4e099bfb565dd2c9816ea830a97d8aa0 - PSD_Sparse_Stand_Walk_Pivots BeginCache
[2025.06.14-08.16.14:873][  0]LogPoseSearch: 7422884d3ff8981c6046792dc10fe09a4c24308a - PSD_Sparse_Stand_Walk_Starts BuildIndex From Cache
[2025.06.14-08.16.14:875][  0]LogPoseSearch: 9b1e0fcf4e099bfb565dd2c9816ea830a97d8aa0 - PSD_Sparse_Stand_Walk_Pivots BuildIndex From Cache
[2025.06.14-08.16.14:875][  0]LogPoseSearch: 1c16dd5633e08ef3b597050bd78d21dbaef80b32 - PSD_Sparse_Stand_Walk_Loops BeginCache
[2025.06.14-08.16.14:876][  0]LogPoseSearch: 1c16dd5633e08ef3b597050bd78d21dbaef80b32 - PSD_Sparse_Stand_Walk_Loops BuildIndex From Cache
[2025.06.14-08.16.14:876][  0]LogPoseSearch: 27fc346f65a9c742a6fd206e1e65abb7e2edf4bc - PSD_Sparse_Stand_Run_Stops BeginCache
[2025.06.14-08.16.14:877][  0]LogPoseSearch: 27fc346f65a9c742a6fd206e1e65abb7e2edf4bc - PSD_Sparse_Stand_Run_Stops BuildIndex From Cache
[2025.06.14-08.16.14:878][  0]LogPoseSearch: d3d6ef9b6b2ffd62ecebcf81e17aebbef6160064 - PSD_Sparse_Stand_Run_Starts BeginCache
[2025.06.14-08.16.14:878][  0]LogPoseSearch: d3d6ef9b6b2ffd62ecebcf81e17aebbef6160064 - PSD_Sparse_Stand_Run_Starts BuildIndex From Cache
[2025.06.14-08.16.14:879][  0]LogPoseSearch: 08ad999e0dcd075fd9c97072373dcbb615b8feca - PSD_Sparse_Stand_Run_Pivots BeginCache
[2025.06.14-08.16.14:879][  0]LogSourceControl: P4 execution time: 0.1628 seconds. Command: client -o super_dev
[2025.06.14-08.16.14:880][  0]LogPoseSearch: 08ad999e0dcd075fd9c97072373dcbb615b8feca - PSD_Sparse_Stand_Run_Pivots BuildIndex From Cache
[2025.06.14-08.16.14:880][  0]LogPoseSearch: c2b794473f3b5bdff20d5ae1c7aa347380d8df35 - PSD_Sparse_Stand_Run_Loops BeginCache
[2025.06.14-08.16.14:881][  0]LogPoseSearch: c2b794473f3b5bdff20d5ae1c7aa347380d8df35 - PSD_Sparse_Stand_Run_Loops BuildIndex From Cache
[2025.06.14-08.16.14:882][  0]LogPoseSearch: a90e404afacab09c0c01fea3014b56a4b41b8efc - PSD_Dense_Stand_Walk_Stops BeginCache
[2025.06.14-08.16.14:883][  0]LogPoseSearch: a90e404afacab09c0c01fea3014b56a4b41b8efc - PSD_Dense_Stand_Walk_Stops BuildIndex From Cache
[2025.06.14-08.16.14:883][  0]LogPoseSearch: d0dc4d6ef5cd34cdd65c44eb4a2163c674794790 - PSD_Dense_Stand_TurnInPlace BeginCache
[2025.06.14-08.16.14:884][  0]LogPoseSearch: d0dc4d6ef5cd34cdd65c44eb4a2163c674794790 - PSD_Dense_Stand_TurnInPlace BuildIndex From Cache
[2025.06.14-08.16.14:885][  0]LogPoseSearch: 4c49396c7b6f0a522a32fc7675d4e0ca4b010e87 - PSD_Dense_Stand_Run_FromTraversal BeginCache
[2025.06.14-08.16.14:885][  0]LogPoseSearch: 4c49396c7b6f0a522a32fc7675d4e0ca4b010e87 - PSD_Dense_Stand_Run_FromTraversal BuildIndex From Cache
[2025.06.14-08.16.14:886][  0]LogPoseSearch: d8308904b8d57c8efd67124f6306263dc9f9d3d1 - PSD_Dense_Stand_Walk_Starts BeginCache
[2025.06.14-08.16.14:887][  0]LogPoseSearch: 1d0efb1277001e6aab96677ea275aa49a3639d53 - PSD_Dense_Stand_Walk_Pivots BeginCache
[2025.06.14-08.16.14:889][  0]LogPoseSearch: 925140107328f20ea73153a3e5fb5ae130b274fd - PSD_Dense_Stand_Walk_Loops BeginCache
[2025.06.14-08.16.14:890][  0]LogPoseSearch: 56edd2bae11544f6faf4c5813eb6d9dea82406ca - PSD_Dense_Stand_Walk_Lands_Light BeginCache
[2025.06.14-08.16.14:892][  0]LogPoseSearch: e5c4b6691010eb49b0a58b2cf25a6c4cd3a0c1f9 - PSD_Dense_Stand_Run_Stops BeginCache
[2025.06.14-08.16.14:893][  0]LogPoseSearch: c0e00f30ffec23002b312e898e49d76fc19b4709 - PSD_Dense_Stand_Run_Starts BeginCache
[2025.06.14-08.16.14:895][  0]LogPoseSearch: 479f81cbf95074a758c64a60f455e4c2439389a0 - PSD_Dense_Stand_Run_Pivots BeginCache
[2025.06.14-08.16.14:896][  0]LogPoseSearch: 39cf02bc894c8005e386a3220b15b694e2f7a0f4 - PSD_Dense_Stand_Run_Loops BeginCache
[2025.06.14-08.16.14:898][  0]LogPoseSearch: 8fd2914d28001d2c4c0cbcbff3a30ab9c34712b6 - PSD_Dense_Stand_Run_Lands_Light BeginCache
[2025.06.14-08.16.14:900][  0]LogPoseSearch: 0314ccbbf566b56ff239f2a3549e5e9c834c5e15 - PSD_Dense_Stand_Idles BeginCache
[2025.06.14-08.16.14:901][  0]LogPoseSearch: 544b8a96ced68cb23597b74779436bec1c0062ce - PSD_Dense_Stand_Idle_Lands_Light BeginCache
[2025.06.14-08.16.14:901][  0]LogPoseSearch: 925140107328f20ea73153a3e5fb5ae130b274fd - PSD_Dense_Stand_Walk_Loops BuildIndex From Cache
[2025.06.14-08.16.14:901][  0]LogPoseSearch: d8308904b8d57c8efd67124f6306263dc9f9d3d1 - PSD_Dense_Stand_Walk_Starts BuildIndex From Cache
[2025.06.14-08.16.14:902][  0]LogPoseSearch: 56edd2bae11544f6faf4c5813eb6d9dea82406ca - PSD_Dense_Stand_Walk_Lands_Light BuildIndex From Cache
[2025.06.14-08.16.14:902][  0]LogPoseSearch: 1d0efb1277001e6aab96677ea275aa49a3639d53 - PSD_Dense_Stand_Walk_Pivots BuildIndex From Cache
[2025.06.14-08.16.14:902][  0]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.14-08.16.14:902][  0]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.14-08.16.14:902][  0]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/14 13:44:49
[2025.06.14-08.16.14:902][  0]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.14-08.16.14:902][  0]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.14-08.16.14:902][  0]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.14-08.16.14:902][  0]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.14-08.16.14:902][  0]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.14-08.16.14:902][  0]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.14-08.16.14:902][  0]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.14-08.16.14:902][  0]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.14-08.16.14:902][  0]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.14-08.16.14:902][  0]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.14-08.16.14:902][  0]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.14-08.16.14:902][  0]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.14-08.16.14:902][  0]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.14-08.16.14:902][  0]LogPoseSearch: e5c4b6691010eb49b0a58b2cf25a6c4cd3a0c1f9 - PSD_Dense_Stand_Run_Stops BuildIndex From Cache
[2025.06.14-08.16.14:902][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.06.14-08.16.14:902][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.06.14-08.16.14:902][  0]LogPoseSearch: c0e00f30ffec23002b312e898e49d76fc19b4709 - PSD_Dense_Stand_Run_Starts BuildIndex From Cache
[2025.06.14-08.16.14:902][  0]LogPoseSearch: 39cf02bc894c8005e386a3220b15b694e2f7a0f4 - PSD_Dense_Stand_Run_Loops BuildIndex From Cache
[2025.06.14-08.16.14:902][  0]LogPoseSearch: 8fd2914d28001d2c4c0cbcbff3a30ab9c34712b6 - PSD_Dense_Stand_Run_Lands_Light BuildIndex From Cache
[2025.06.14-08.16.14:902][  0]LogPoseSearch: 0314ccbbf566b56ff239f2a3549e5e9c834c5e15 - PSD_Dense_Stand_Idles BuildIndex From Cache
[2025.06.14-08.16.14:902][  0]LogPoseSearch: 544b8a96ced68cb23597b74779436bec1c0062ce - PSD_Dense_Stand_Idle_Lands_Light BuildIndex From Cache
[2025.06.14-08.16.14:903][  0]LogPoseSearch: 479f81cbf95074a758c64a60f455e4c2439389a0 - PSD_Dense_Stand_Run_Pivots BuildIndex From Cache
[2025.06.14-08.16.15:140][  0]LogSlate: Took 0.000240 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.06.14-08.16.15:142][  0]LogSlate: Took 0.000132 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Italic.ttf' (157K)
[2025.06.14-08.16.15:153][  0]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Config/DefaultEngine.ini'
[2025.06.14-08.16.15:206][  0]LogNNEDenoiser: ApplySettings: bDenoiserEnabled 1
[2025.06.14-08.16.15:206][  0]LogStreaming: Display: FlushAsyncLoading(342): 1 QueuedPackages, 0 AsyncPackages
[2025.06.14-08.16.15:209][  0]LogNNEDenoiser: Loaded input mapping from NNEDIM_ColorAlbedoNormal_Alpha
[2025.06.14-08.16.15:209][  0]LogNNEDenoiser: Loaded output mapping from NNEDOM_Output_Alpha
[2025.06.14-08.16.15:210][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.06.14-08.16.15:274][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.06.14-08.16.15:274][  0]LogNNEDenoiser: Create denoiser from asset /NNEDenoiser/NNED_Oidn2-3_Balanced_Alpha.NNED_Oidn2-3_Balanced_Alpha...
[2025.06.14-08.16.15:275][  0]LogNNEDenoiser: Loaded input mapping from NNEDTIM_ColorAlbedoNormal_Alpha
[2025.06.14-08.16.15:275][  0]LogNNEDenoiser: Loaded output mapping from NNEDTOM_Output_Alpha
[2025.06.14-08.16.15:275][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.06.14-08.16.15:319][  0]LogSourceControl: P4 execution time: 0.1667 seconds. Command: fstat -Or H:/P4/dev/Baoli/Config/DefaultEngine.ini
[2025.06.14-08.16.15:329][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.06.14-08.16.15:329][  0]LogNNEDenoiser: Create temporal denoiser from asset /NNEDenoiser/NNEDT_Oidn2-3_Balanced_Alpha.NNEDT_Oidn2-3_Balanced_Alpha...
[2025.06.14-08.16.15:371][  0]LogSlate: Took 0.000559 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.06.14-08.16.15:852][  0]LogD3D12RHI: Creating RTPSO with 197 shaders (0 cached, 197 new) took 60.53 ms. Compile time 53.91 ms, link time 6.40 ms.
[2025.06.14-08.16.15:863][  0]LogD3D12RHI: Creating RTPSO with 24 shaders (0 cached, 24 new) took 34.14 ms. Compile time 9.69 ms, link time 24.42 ms.
[2025.06.14-08.16.15:957][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.14-08.16.15:966][  0]LogFab: Error: Login failed - error code: EOS_InvalidAuth
[2025.06.14-08.16.15:966][  0]LogFab: Display: Logging in using exchange code
[2025.06.14-08.16.15:966][  0]LogFab: Display: Reading exchange code from commandline
[2025.06.14-08.16.15:966][  0]LogEOSSDK: Error: LogEOSAuth: Invalid parameter EOS_Auth_Credentials.Token reason: must not be null or empty
[2025.06.14-08.16.15:966][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Importers/USDImporter/Content/Python/init_unreal.py... started...
[2025.06.14-08.16.15:966][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Importers/USDImporter/Content/Python/init_unreal.py... took 187 us
[2025.06.14-08.16.15:966][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.06.14-08.16.15:998][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.06.14-08.16.16:012][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 45.442 ms (total: 45.629 ms)
[2025.06.14-08.16.16:012][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/Black_1 which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.14-08.16.16:012][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Body, a dependent package /Game/Assets/Kettle/Black_1 was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/Black_1 has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/Black_1.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.14-08.16.16:012][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/Black_Glossy which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.14-08.16.16:012][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Body, a dependent package /Game/Assets/Kettle/Black_Glossy was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/Black_Glossy has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/Black_Glossy.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.14-08.16.16:012][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/material which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.14-08.16.16:013][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Body, a dependent package /Game/Assets/Kettle/material was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/material has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/material.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.14-08.16.16:013][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/Black_1 which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.14-08.16.16:013][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Lid, a dependent package /Game/Assets/Kettle/Black_1 was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/Black_1 has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/Black_1.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.14-08.16.16:013][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath H:/P4/dev/Baoli/Content/Assets/Kettle/Black_Dark which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.14-08.16.16:013][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Kettle/Lid, a dependent package /Game/Assets/Kettle/Black_Dark was not available. Additional explanatory information follows:
FPackageName: Skipped package /Game/Assets/Kettle/Black_Dark has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'H:/P4/dev/Baoli/Content/Assets/Kettle/Black_Dark.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.14-08.16.16:013][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath ../../Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.14-08.16.16:013][  0]LoadErrors: Warning: While trying to load package /Game/Assets/TV/TVfront, a dependent package /Interchange/gltf/MaterialInstances/MI_Default_Opaque was not available. Additional explanatory information follows:
FPackageName: Skipped package /Interchange/gltf/MaterialInstances/MI_Default_Opaque has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'D:/UE_5.5/Engine/Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.14-08.16.16:013][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath ../../Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.14-08.16.16:013][  0]LoadErrors: Warning: While trying to load package /Game/Assets/TV/TVback, a dependent package /Interchange/gltf/MaterialInstances/MI_Default_Opaque was not available. Additional explanatory information follows:
FPackageName: Skipped package /Interchange/gltf/MaterialInstances/MI_Default_Opaque has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'D:/UE_5.5/Engine/Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.14-08.16.16:013][  0]LogPackageName: Warning: GetLocalFullPath called on FPackagePath ../../Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque_DS which has an unspecified header extension, and the path does not exist on disk. Assuming EPackageExtension::Asset.
[2025.06.14-08.16.16:013][  0]LoadErrors: Warning: While trying to load package /Game/Assets/Washingmachine/steel, a dependent package /Interchange/gltf/MaterialInstances/MI_Default_Opaque_DS was not available. Additional explanatory information follows:
FPackageName: Skipped package /Interchange/gltf/MaterialInstances/MI_Default_Opaque_DS has a valid, mounted, mount point but does not exist either on disk or in iostore. The uncooked file would be expected on disk at 'D:/UE_5.5/Engine/Plugins/Interchange/Runtime/Content/gltf/MaterialInstances/MI_Default_Opaque_DS.uasset'. Perhaps it has been deleted or was not synced?

[2025.06.14-08.16.16:013][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: BaoliEditor Win64 Development
[2025.06.14-08.16.16:146][  1]LogAssetRegistry: AssetRegistryGather time 0.1601s: AssetDataDiscovery 0.0220s, AssetDataGather 0.0243s, StoreResults 0.1137s. Wall time 37.9510s.
	NumCachedDirectories 0. NumUncachedDirectories 3071. NumCachedFiles 16175. NumUncachedFiles 0.
	BackgroundTickInterruptions 2.
[2025.06.14-08.16.16:214][  1]LogSourceControl: Uncontrolled asset enumeration started...
[2025.06.14-08.16.16:217][  1]LogCollectionManager: Fixed up redirectors for 1 collections in 0.000025 seconds (updated 1 objects)
[2025.06.14-08.16.16:342][  1]LogMaterial: Display: Material /InterchangeAssets/gltf/M_Default.M_Default needed to have new flag set bUsedWithNanite !
[2025.06.14-08.16.16:357][  1]MapCheck: Warning: M_Default Material /InterchangeAssets/gltf/M_Default.M_Default was missing the usage flag bUsedWithNanite. If the material asset is not re-saved, it may not render correctly when run outside the editor. Fix
[2025.06.14-08.16.16:482][  1]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.14-08.16.16:587][  1]LogFab: Error: Login failed - error code: EOS_InvalidParameters
[2025.06.14-08.16.16:626][  1]LogSourceControl: Uncontrolled asset enumeration finished in 0.411957 seconds (Found 7984 uncontrolled assets)
[2025.06.14-08.16.17:336][ 11]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 35.483528
[2025.06.14-08.16.17:336][ 11]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.06.14-08.16.17:338][ 11]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 35.496910
[2025.06.14-08.16.17:632][ 47]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.06.14-08.16.18:031][ 70]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_0
[2025.06.14-08.16.18:041][ 70]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.CBP_SandboxCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.16.18:041][ 70]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_0:PersistentLevel.CBP_SandboxCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.16.18:129][ 70]LogD3D12RHI: Creating RTPSO with 228 shaders (224 cached, 4 new) took 14.22 ms. Compile time 8.01 ms, link time 6.15 ms.
[2025.06.14-08.16.18:269][ 71]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 36.368839
[2025.06.14-08.16.18:271][ 71]LogEOSSDK: LogEOS: SDK Config Data - Watermark: -987497851
[2025.06.14-08.16.18:271][ 71]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 36.368839, Update Interval: 349.198273
[2025.06.14-08.16.18:369][ 73]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_1
[2025.06.14-08.16.18:540][ 77]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_2
[2025.06.14-08.16.18:583][ 78]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_3
[2025.06.14-08.16.18:591][ 78]LogActor: Warning: BP_Bed_C /Engine/Transient.World_3:PersistentLevel.BP_Bed_C_0 has natively added scene component(s), but none of them were set as the actor's RootComponent - picking one arbitrarily
[2025.06.14-08.16.18:629][ 79]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_4
[2025.06.14-08.16.19:071][ 87]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset H:/P4/dev/Baoli/Content/Blueprints/BaoliPlayerCameraManager.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_Almirah.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliController.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_Bed.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_Bulb.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_ChestActorPawn.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_Fan.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_FlashLight.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_GameController.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_GameMinimal.uasset H:/P4/dev/Baoli/Content/Blueprints/CBP_SandboxCharacter.uasset H:/P4/dev/Baoli/Content/Blueprints/GM_Sandbox.uasset H:/P4/dev/Baoli/Content/Blueprints/Kettle_BP.uasset H:/P4/dev/Baoli/Content/Blueprints/QTE.uasset'
[2025.06.14-08.16.19:083][ 87]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_5
[2025.06.14-08.16.19:089][ 87]LogActor: Warning: BP_Almirah_C /Engine/Transient.World_5:PersistentLevel.BP_Almirah_C_0 has natively added scene component(s), but none of them were set as the actor's RootComponent - picking one arbitrarily
[2025.06.14-08.16.19:126][ 88]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_6
[2025.06.14-08.16.19:134][ 88]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.16.19:134][ 88]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_0
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.16.19:323][ 94]LogSourceControl: P4 execution time: 0.2529 seconds. Command: fstat -Or H:/P4/dev/Baoli/Content/Blueprints/ABP_SandboxCharacter.uasset H:/P4/dev/Baoli/Content/Blueprints/BaoliPlayerCameraManager.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_Almirah.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliController.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_Bed.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_Bulb.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_ChestActorPawn.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_Fan.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_FlashLight.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_GameController.uasset H:/P4/dev/Baoli/Content/Blueprints/BP_GameMinimal.uasset H:/P4/dev/Baoli/Content/Blueprints/CBP_SandboxCharacter.uasset H:/P4/dev/Baoli/Content/Blueprints/GM_Sandbox.uasset H:/P4/dev/Baoli/Content/Blueprints/Kettle_BP.uasset H:/P4/dev/Baoli/Content/Blueprints/QTE.uasset
[2025.06.14-08.16.21:241][164]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.14-08.16.21:247][164]LogPlayLevel: PlayLevel: No blueprints needed recompiling
[2025.06.14-08.16.21:252][164]LogOnline: OSS: Created online subsystem instance for: NULL
[2025.06.14-08.16.21:252][164]LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
[2025.06.14-08.16.21:252][164]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.14-08.16.21:316][164]LogPlayLevel: PIE: StaticDuplicateObject took: (0.064467s)
[2025.06.14-08.16.21:316][164]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.064514s)
[2025.06.14-08.16.21:401][164]LogUObjectHash: Compacting FUObjectHashTables data took   1.07ms
[2025.06.14-08.16.21:405][164]r.RayTracing.Culling = "0"
[2025.06.14-08.16.21:405][164]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.14-08.16.21:405][164]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.14-08.16.21:407][164]LogPlayLevel: PIE: World Init took: (0.001834s)
[2025.06.14-08.16.21:408][164]LogAudio: Display: Creating Audio Device:                 Id: 2, Scope: Unique, Realtime: True
[2025.06.14-08.16.21:408][164]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.14-08.16.21:408][164]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.14-08.16.21:408][164]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.14-08.16.21:408][164]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.14-08.16.21:408][164]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.14-08.16.21:408][164]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.14-08.16.21:408][164]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.14-08.16.21:409][164]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.14-08.16.21:409][164]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.14-08.16.21:409][164]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.14-08.16.21:409][164]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.14-08.16.21:411][164]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.14-08.16.21:449][164]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.14-08.16.21:450][164]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.14-08.16.21:450][164]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.14-08.16.21:450][164]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.14-08.16.21:450][164]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=2
[2025.06.14-08.16.21:451][164]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=2
[2025.06.14-08.16.21:454][164]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=2
[2025.06.14-08.16.21:454][164]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=2
[2025.06.14-08.16.21:454][164]LogInit: FAudioDevice initialized with ID 2.
[2025.06.14-08.16.21:454][164]LogAudio: Display: Audio Device (ID: 2) registered with world 'DefaultLevel'.
[2025.06.14-08.16.21:454][164]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 2
[2025.06.14-08.16.21:454][164]LogWindows: WindowsPlatformFeatures enabled
[2025.06.14-08.16.21:454][164]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.14-08.16.21:456][164]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.14-08.16.21:489][164]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.14-08.16.21:501][164]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.14-13.46.21
[2025.06.14-08.16.21:511][164]LogWorld: Bringing up level for play took: 0.053706
[2025.06.14-08.16.21:514][164]LogOnline: OSS: Created online subsystem instance for: :Context_8
[2025.06.14-08.16.21:515][164]LogAnimation: Warning: SetBlendSpace called on an invalid context or with an invalid type
[2025.06.14-08.16.21:533][164]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.14-08.16.21:533][164]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.14-08.16.21:533][164]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.14-08.16.21:533][164]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.14-08.16.21:533][164]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.14-08.16.21:533][164]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.14-08.16.21:533][164]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.14-08.16.21:533][164]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.14-08.16.21:533][164]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.14-08.16.21:533][164]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.14-08.16.21:533][164]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.14-08.16.21:533][164]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.14-08.16.21:533][164]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.14-08.16.21:533][164]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.14-08.16.21:533][164]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.14-08.16.21:533][164]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.14-08.16.21:549][164]PIE: Server logged in
[2025.06.14-08.16.21:550][164]PIE: Play in editor total start time 0.303 seconds.
[2025.06.14-08.16.21:554][164]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 7.0
[2025.06.14-08.16.21:803][164]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.14-08.16.21:849][164]LogD3D12RHI: Creating RTPSO with 24 shaders (20 cached, 4 new) took 15.13 ms. Compile time 12.61 ms, link time 2.47 ms.
[2025.06.14-08.16.21:858][164]LogD3D12RHI: Creating RTPSO with 228 shaders (0 cached, 1 new) took 23.86 ms. Compile time 17.74 ms, link time 5.95 ms.
[2025.06.14-08.16.21:965][165]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.22:042][166]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.22:127][167]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.22:201][168]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.22:375][169]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.22:457][170]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.22:494][171]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.22:532][172]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_1
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.16.22:533][172]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_1
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.16.22:535][172]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.22:551][173]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.22:578][174]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.22:631][175]LogMetaSound: Display: Auto-Updating '/Game/Audio/Foley/MetaSounds/MSS_FoleySound.MSS_FoleySound' node class 'UE.Wave Player.Stereo (v1.0)': Interface change detected.
[2025.06.14-08.16.22:631][175]LogMetaSound: Display: Auto-Updating '/Game/Audio/Foley/MetaSounds/MSS_FoleySound.MSS_FoleySound' node class 'Array.Random Get.WaveAsset:Array': Newer version 'v1.1' found.
[2025.06.14-08.16.22:637][175]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.22:681][176]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.22:702][177]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.22:746][178]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.22:798][179]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.22:831][180]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.22:869][181]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.22:889][182]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.22:912][183]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.22:954][184]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.22:982][185]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:012][186]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:051][187]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:073][188]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:107][189]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:150][190]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:189][191]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:222][192]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:256][193]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:293][194]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:348][195]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:387][196]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:454][197]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:480][198]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:500][199]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:538][200]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:559][201]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:583][202]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:644][203]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:656][204]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:672][205]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:699][206]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:723][207]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:749][208]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:774][209]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:801][210]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:823][211]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:847][212]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:869][213]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:892][214]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:915][215]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:941][216]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:975][217]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.23:998][218]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.24:022][219]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.24:051][220]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.24:073][221]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.24:096][222]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.24:119][223]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.24:147][224]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.24:173][225]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.24:202][226]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.24:225][227]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.24:249][228]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.24:273][229]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.24:299][230]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.24:326][231]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.24:352][232]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.24:380][233]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.24:412][234]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.24:444][235]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.24:497][236]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.24:602][237]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.24:700][238]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.24:729][239]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.24:756][240]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.24:791][241]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.24:820][242]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_2
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.16.24:820][242]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_2
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.16.24:822][242]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.24:841][243]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.24:857][244]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.24:886][245]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.24:909][246]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.24:932][247]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.24:959][248]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.24:984][249]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:007][250]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:031][251]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:061][252]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:085][253]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:111][254]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:144][255]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:170][256]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:191][257]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:214][258]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:238][259]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:263][260]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:287][261]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:320][262]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:345][263]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:376][264]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:401][265]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:432][266]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:457][267]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:485][268]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:521][269]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:549][270]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:578][271]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:603][272]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:632][273]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:656][274]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:683][275]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:713][276]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:740][277]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:767][278]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:795][279]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:823][280]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:849][281]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:874][282]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:903][283]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:931][284]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:962][285]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.25:989][286]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:018][287]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:044][288]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:069][289]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:095][290]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:126][291]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:151][292]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:181][293]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:208][294]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:234][295]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:261][296]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:289][297]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:313][298]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:340][299]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:367][300]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:397][301]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:425][302]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:452][303]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:477][304]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:504][305]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:531][306]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:557][307]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:581][308]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:606][309]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:630][310]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:655][311]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:679][312]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:703][313]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:729][314]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:751][315]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:775][316]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:801][317]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:829][318]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:851][319]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:875][320]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:901][321]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:921][322]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:946][323]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:970][324]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.26:995][325]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:018][326]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:048][327]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:068][328]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:126][329]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:137][330]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:154][331]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:178][332]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:201][333]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:224][334]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:251][335]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:273][336]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:301][337]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:327][338]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:350][339]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:373][340]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:398][341]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:420][342]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:445][343]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:470][344]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:495][345]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:525][346]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:550][347]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:572][348]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:595][349]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:617][350]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:640][351]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:663][352]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:686][353]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:711][354]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:734][355]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:763][356]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:791][357]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:821][358]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:846][359]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:871][360]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:898][361]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:926][362]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:957][363]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.27:983][364]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.28:008][365]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.28:032][366]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.28:058][367]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.28:085][368]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.28:111][369]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.28:137][370]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.28:166][371]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.28:193][372]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.28:220][373]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.28:249][374]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.28:275][375]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.28:300][376]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.28:328][377]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.28:357][378]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.28:385][379]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.28:410][380]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.28:435][381]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.28:461][382]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.28:487][383]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.28:516][384]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.28:544][385]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.28:572][386]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.28:600][387]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.28:626][388]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.28:652][389]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.28:678][390]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.28:703][391]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.28:728][392]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.28:754][393]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.28:783][394]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.28:809][395]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.28:836][396]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.28:862][397]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.28:887][398]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.28:913][399]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.28:938][400]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.28:965][401]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.28:993][402]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.29:021][403]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.29:048][404]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.29:077][405]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.29:104][406]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.29:130][407]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.29:154][408]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.29:182][409]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.29:211][410]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.29:238][411]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.29:262][412]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.29:287][413]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.29:312][414]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.29:339][415]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.29:366][416]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.0
[2025.06.14-08.16.29:398][417]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.082404
[2025.06.14-08.16.29:428][418]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.358912
[2025.06.14-08.16.29:457][419]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.619025
[2025.06.14-08.16.29:484][420]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 1.865
[2025.06.14-08.16.29:511][421]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 2.117209
[2025.06.14-08.16.29:537][422]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 2.365056
[2025.06.14-08.16.29:561][423]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 2.597301
[2025.06.14-08.16.29:588][424]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 2.823311
[2025.06.14-08.16.29:614][425]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 3.065293
[2025.06.14-08.16.29:641][426]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 3.299466
[2025.06.14-08.16.29:669][427]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 3.540901
[2025.06.14-08.16.29:702][428]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 3.787463
[2025.06.14-08.16.29:729][429]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 4.061309
[2025.06.14-08.16.29:754][430]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 4.317268
[2025.06.14-08.16.29:782][431]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 4.554348
[2025.06.14-08.16.29:807][432]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 4.801362
[2025.06.14-08.16.29:833][433]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 5.038787
[2025.06.14-08.16.29:858][434]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 5.2734
[2025.06.14-08.16.29:889][435]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 5.502951
[2025.06.14-08.16.29:919][436]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 5.780208
[2025.06.14-08.16.29:947][437]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 6.051914
[2025.06.14-08.16.29:973][438]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 6.300616
[2025.06.14-08.16.29:999][439]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 6.537994
[2025.06.14-08.16.30:026][440]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 6.768802
[2025.06.14-08.16.30:055][441]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 7.005157
[2025.06.14-08.16.30:086][442]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 7.263712
[2025.06.14-08.16.30:112][443]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 7.518027
[2025.06.14-08.16.30:138][444]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 7.761351
[2025.06.14-08.16.30:164][445]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 7.996253
[2025.06.14-08.16.30:193][446]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 8.244327
[2025.06.14-08.16.30:217][447]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 8.493173
[2025.06.14-08.16.30:247][448]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 8.723986
[2025.06.14-08.16.30:270][449]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 8.975707
[2025.06.14-08.16.30:298][450]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 9.202865
[2025.06.14-08.16.30:325][451]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 9.439223
[2025.06.14-08.16.30:353][452]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 9.69048
[2025.06.14-08.16.30:382][453]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 9.948776
[2025.06.14-08.16.30:410][454]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.30:437][455]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.30:467][456]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.30:497][457]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.30:528][458]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.30:560][459]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.30:587][460]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.30:614][461]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.30:644][462]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.30:670][463]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.30:698][464]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.30:727][465]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.30:757][466]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.30:785][467]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.30:811][468]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.30:836][469]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.30:863][470]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.30:891][471]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.30:918][472]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.30:943][473]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.30:972][474]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:026][475]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:077][476]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:112][477]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:139][478]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:171][479]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:204][480]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:240][481]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:280][482]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:311][483]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:341][484]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:369][485]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:396][486]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:423][487]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:454][488]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:481][489]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:507][490]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:538][491]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:564][492]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:594][493]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:619][494]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:647][495]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:673][496]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:702][497]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:731][498]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:755][499]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:783][500]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:808][501]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:833][502]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:859][503]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:886][504]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:913][505]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:939][506]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:964][507]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.31:992][508]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:019][509]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:046][510]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:073][511]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:102][512]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:132][513]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:157][514]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:182][515]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:209][516]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:236][517]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:261][518]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:294][519]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:321][520]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:345][521]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:369][522]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:396][523]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:421][524]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:447][525]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:473][526]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:499][527]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:524][528]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:552][529]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:579][530]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:603][531]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:629][532]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:656][533]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:682][534]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:709][535]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:737][536]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:764][537]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:790][538]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:814][539]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:840][540]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:866][541]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:890][542]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:917][543]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:943][544]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:970][545]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.32:998][546]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:023][547]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:050][548]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:075][549]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:100][550]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:127][551]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:153][552]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:180][553]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:206][554]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:233][555]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:258][556]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:285][557]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:310][558]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:336][559]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:364][560]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:393][561]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:419][562]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:447][563]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:474][564]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:503][565]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:534][566]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:564][567]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:594][568]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:620][569]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:646][570]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:673][571]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:699][572]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:726][573]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:752][574]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:779][575]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:808][576]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:838][577]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:867][578]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:890][579]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:913][580]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:938][581]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:966][582]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.33:991][583]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:018][584]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:045][585]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:071][586]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:098][587]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:123][588]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:149][589]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:174][590]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:201][591]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:228][592]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:259][593]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:285][594]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:309][595]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:337][596]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:364][597]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:391][598]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:418][599]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:445][600]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:474][601]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:499][602]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:530][603]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:556][604]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:586][605]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:610][606]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:636][607]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:662][608]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:692][609]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:718][610]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:743][611]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:768][612]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:795][613]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:821][614]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:847][615]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:873][616]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:903][617]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:931][618]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:957][619]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.34:984][620]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:010][621]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:035][622]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:062][623]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:089][624]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:118][625]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:142][626]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:168][627]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:193][628]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:218][629]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:245][630]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:271][631]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:297][632]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:324][633]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:351][634]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:377][635]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:402][636]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:427][637]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:451][638]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:478][639]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:505][640]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:534][641]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:560][642]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:586][643]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:615][644]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:642][645]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:668][646]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:694][647]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:721][648]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:748][649]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:774][650]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:800][651]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:825][652]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:852][653]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:877][654]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:902][655]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:929][656]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:956][657]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.35:983][658]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:008][659]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:033][660]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:058][661]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:085][662]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:110][663]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:139][664]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:167][665]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:193][666]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:219][667]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:245][668]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:270][669]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:295][670]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:320][671]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:349][672]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:377][673]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:402][674]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:429][675]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:453][676]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:479][677]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:505][678]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:531][679]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:557][680]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:586][681]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:612][682]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:637][683]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:663][684]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:688][685]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:713][686]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:740][687]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:768][688]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:797][689]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:822][690]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:849][691]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:874][692]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:901][693]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:929][694]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:954][695]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.36:982][696]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:009][697]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:035][698]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:087][699]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:115][700]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:141][701]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:168][702]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:196][703]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:226][704]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:252][705]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:278][706]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:305][707]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:330][708]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:354][709]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:386][710]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:413][711]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:440][712]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:465][713]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:491][714]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:519][715]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:548][716]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:572][717]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:598][718]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:623][719]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:651][720]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:678][721]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:706][722]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:731][723]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:755][724]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:781][725]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:811][726]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:837][727]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:866][728]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:893][729]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:919][730]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:944][731]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:969][732]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.37:995][733]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:020][734]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:047][735]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:074][736]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:101][737]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:127][738]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:152][739]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:178][740]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:202][741]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:229][742]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:254][743]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:282][744]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:308][745]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:336][746]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:364][747]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:391][748]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:417][749]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:443][750]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:469][751]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:497][752]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:525][753]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:552][754]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:578][755]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:605][756]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:632][757]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:658][758]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:685][759]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:712][760]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:741][761]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:766][762]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:792][763]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:819][764]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:846][765]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:881][766]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:912][767]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:946][768]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:972][769]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.38:998][770]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:025][771]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:052][772]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:077][773]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:104][774]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:132][775]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:160][776]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:186][777]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:214][778]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:240][779]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:269][780]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:294][781]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:321][782]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:349][783]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:380][784]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:406][785]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:433][786]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:457][787]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:487][788]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:512][789]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:540][790]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:566][791]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:593][792]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:619][793]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:647][794]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:672][795]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:697][796]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:722][797]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:749][798]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:778][799]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:804][800]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:831][801]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:859][802]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:884][803]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:910][804]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:938][805]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:963][806]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.39:989][807]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:017][808]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:044][809]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:071][810]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:097][811]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:123][812]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:148][813]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:174][814]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:201][815]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:228][816]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:256][817]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:283][818]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:312][819]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:337][820]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:362][821]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:386][822]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:413][823]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:442][824]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:469][825]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:496][826]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:523][827]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:549][828]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:575][829]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:601][830]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:628][831]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:655][832]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:682][833]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:707][834]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:734][835]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:761][836]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:787][837]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:812][838]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:838][839]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:865][840]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:894][841]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:919][842]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:945][843]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:972][844]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.40:998][845]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.41:023][846]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.41:049][847]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.41:076][848]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.41:101][849]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.41:137][850]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.41:172][851]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.41:208][852]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.41:249][853]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.41:292][854]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.41:334][855]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.41:374][856]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.41:418][857]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.41:451][858]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.41:492][859]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.41:538][860]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.41:582][861]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.41:622][862]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.41:655][863]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.41:683][864]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.41:712][865]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.41:739][866]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.41:779][867]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.41:820][868]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.41:857][869]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.41:905][870]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.41:939][871]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.41:977][872]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:019][873]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:067][874]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:101][875]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:130][876]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:158][877]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:186][878]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:216][879]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:244][880]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:276][881]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:302][882]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:327][883]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:355][884]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:383][885]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:411][886]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:438][887]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:465][888]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:492][889]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:519][890]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:547][891]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:575][892]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:603][893]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:627][894]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:653][895]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:679][896]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:704][897]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:734][898]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:760][899]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:785][900]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:811][901]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:835][902]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:861][903]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:888][904]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:911][905]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:937][906]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:959][907]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.42:985][908]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:009][909]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:028][910]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:055][911]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:081][912]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:109][913]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:137][914]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:166][915]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:193][916]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:219][917]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:245][918]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:270][919]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:297][920]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:323][921]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:350][922]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:378][923]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:410][924]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:438][925]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:464][926]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:490][927]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:515][928]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:545][929]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:569][930]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:599][931]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:624][932]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:649][933]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:675][934]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:701][935]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:723][936]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:748][937]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:770][938]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:798][939]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:824][940]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:851][941]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:876][942]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:902][943]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:928][944]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:954][945]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.43:981][946]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:008][947]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:034][948]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:059][949]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:083][950]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:106][951]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:130][952]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:152][953]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:175][954]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:198][955]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:222][956]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:246][957]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:271][958]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:296][959]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:317][960]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:339][961]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:363][962]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:389][963]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:414][964]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:438][965]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:465][966]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:494][967]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:518][968]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:543][969]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:567][970]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:589][971]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:612][972]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:635][973]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:660][974]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:685][975]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:712][976]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:736][977]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:759][978]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:782][979]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:805][980]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:828][981]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:851][982]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:874][983]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:901][984]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:926][985]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:952][986]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:974][987]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.44:997][988]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:019][989]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:041][990]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:063][991]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:086][992]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:111][993]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:136][994]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:162][995]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:185][996]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:207][997]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:231][998]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:254][999]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:277][  0]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:300][  1]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:326][  2]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:349][  3]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:375][  4]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:400][  5]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:424][  6]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:449][  7]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:471][  8]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:495][  9]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:521][ 10]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:545][ 11]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:570][ 12]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:595][ 13]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:619][ 14]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:643][ 15]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:667][ 16]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:691][ 17]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:715][ 18]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:738][ 19]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:764][ 20]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:790][ 21]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:816][ 22]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:839][ 23]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:863][ 24]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:887][ 25]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:912][ 26]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:935][ 27]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:962][ 28]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.45:989][ 29]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:028][ 30]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:086][ 31]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:129][ 32]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:151][ 33]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:182][ 34]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:213][ 35]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:242][ 36]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:270][ 37]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:303][ 38]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:333][ 39]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:363][ 40]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:387][ 41]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:414][ 42]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:441][ 43]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:469][ 44]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:494][ 45]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:520][ 46]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:547][ 47]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:570][ 48]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:596][ 49]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:624][ 50]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:652][ 51]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:678][ 52]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:703][ 53]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:730][ 54]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:756][ 55]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:781][ 56]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:808][ 57]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:836][ 58]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:862][ 59]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:889][ 60]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:916][ 61]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:943][ 62]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:968][ 63]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.46:993][ 64]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:019][ 65]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:049][ 66]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:076][ 67]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:102][ 68]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:129][ 69]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:154][ 70]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:185][ 71]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:211][ 72]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:238][ 73]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:268][ 74]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:295][ 75]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:322][ 76]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:349][ 77]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:374][ 78]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:399][ 79]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:425][ 80]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:451][ 81]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:479][ 82]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:506][ 83]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:533][ 84]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:558][ 85]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:584][ 86]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:610][ 87]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:636][ 88]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:662][ 89]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:688][ 90]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:716][ 91]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:743][ 92]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:771][ 93]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:795][ 94]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:822][ 95]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:848][ 96]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:872][ 97]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:902][ 98]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:929][ 99]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:956][100]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.47:981][101]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:006][102]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:033][103]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:059][104]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:085][105]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:113][106]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:140][107]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:167][108]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:193][109]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:219][110]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:244][111]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:270][112]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:298][113]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:328][114]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:354][115]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:381][116]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:407][117]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:432][118]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:460][119]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:486][120]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:512][121]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:539][122]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:568][123]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:595][124]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:621][125]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:647][126]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:672][127]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:697][128]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:722][129]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:751][130]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:780][131]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:809][132]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:836][133]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:863][134]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:890][135]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:915][136]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:943][137]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:970][138]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.48:998][139]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:024][140]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:048][141]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:072][142]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:098][143]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:122][144]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:146][145]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:171][146]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:197][147]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:220][148]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:245][149]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:267][150]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:290][151]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:315][152]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:339][153]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:361][154]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:388][155]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:409][156]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:434][157]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:464][158]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:489][159]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:514][160]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:537][161]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:561][162]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:584][163]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:610][164]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:637][165]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:663][166]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:689][167]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:718][168]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:744][169]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:768][170]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:793][171]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:820][172]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:848][173]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:874][174]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:900][175]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:925][176]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:953][177]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.49:978][178]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:004][179]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:030][180]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:056][181]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:084][182]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:110][183]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:138][184]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:165][185]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:193][186]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:219][187]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:245][188]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:272][189]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:299][190]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:327][191]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:353][192]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:377][193]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:403][194]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:431][195]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:460][196]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:487][197]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:517][198]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:544][199]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:573][200]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:598][201]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:624][202]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:651][203]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:679][204]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:705][205]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:743][206]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:774][207]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:805][208]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:832][209]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:860][210]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:886][211]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:913][212]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:941][213]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:969][214]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.50:996][215]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:023][216]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:050][217]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:077][218]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:107][219]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:138][220]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:166][221]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:193][222]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:219][223]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:244][224]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:269][225]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:294][226]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:319][227]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:347][228]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:379][229]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:405][230]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:433][231]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:459][232]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:486][233]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:511][234]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:537][235]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:564][236]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:592][237]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:617][238]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:642][239]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:666][240]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:693][241]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:719][242]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:742][243]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:766][244]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:791][245]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:838][246]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:865][247]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:888][248]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:912][249]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:936][250]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:962][251]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.51:987][252]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:014][253]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:040][254]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:062][255]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:085][256]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:110][257]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:134][258]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:158][259]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:182][260]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:205][261]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:231][262]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:255][263]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:278][264]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:307][265]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:323][266]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:346][267]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:369][268]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:393][269]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:418][270]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:447][271]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:473][272]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:500][273]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:527][274]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:552][275]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:579][276]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:604][277]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:630][278]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:662][279]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:691][280]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:721][281]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:751][282]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:782][283]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:810][284]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:838][285]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:866][286]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:891][287]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:916][288]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:945][289]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:965][290]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.52:988][291]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:013][292]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:039][293]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:066][294]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:090][295]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:116][296]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:139][297]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:162][298]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:185][299]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:208][300]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:232][301]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:256][302]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:282][303]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:307][304]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:329][305]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:353][306]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:377][307]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:399][308]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:422][309]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:446][310]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:470][311]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:494][312]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:519][313]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:544][314]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:567][315]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:590][316]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:612][317]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:634][318]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:659][319]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:684][320]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:712][321]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:736][322]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:760][323]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:783][324]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:807][325]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:829][326]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:853][327]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:880][328]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:904][329]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:928][330]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:953][331]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:976][332]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.53:999][333]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:022][334]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:044][335]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:068][336]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:091][337]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:116][338]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:141][339]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:165][340]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:188][341]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:211][342]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:234][343]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:258][344]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:283][345]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:306][346]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:331][347]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:355][348]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:382][349]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:406][350]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:430][351]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:453][352]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:475][353]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:497][354]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:522][355]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:548][356]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:573][357]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:598][358]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:621][359]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:645][360]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:668][361]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:691][362]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:716][363]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:739][364]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:765][365]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:792][366]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:818][367]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:842][368]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:866][369]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:889][370]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:912][371]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:935][372]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:961][373]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.54:988][374]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:015][375]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:039][376]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:063][377]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:085][378]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:108][379]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:132][380]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:157][381]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:180][382]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:203][383]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:228][384]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:255][385]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:278][386]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:300][387]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:324][388]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:349][389]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:372][390]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:396][391]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:420][392]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:445][393]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:471][394]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:495][395]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:520][396]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:545][397]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:570][398]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:592][399]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:617][400]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:641][401]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:664][402]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:689][403]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:716][404]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:739][405]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:762][406]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:786][407]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:808][408]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:833][409]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:857][410]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:882][411]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:905][412]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:930][413]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:954][414]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.55:977][415]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:000][416]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:023][417]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:048][418]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:072][419]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:097][420]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:121][421]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:145][422]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:166][423]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:190][424]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:213][425]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:236][426]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:259][427]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:283][428]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:307][429]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:330][430]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:353][431]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:377][432]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:399][433]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:422][434]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:444][435]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:469][436]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:495][437]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:520][438]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:546][439]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:569][440]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:592][441]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:615][442]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:637][443]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:662][444]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:687][445]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:711][446]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:739][447]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:765][448]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:790][449]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:818][450]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:844][451]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:869][452]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:895][453]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:921][454]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:949][455]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.56:978][456]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:002][457]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:030][458]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:054][459]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:082][460]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:107][461]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:134][462]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:161][463]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:188][464]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:215][465]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:242][466]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:268][467]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:292][468]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:318][469]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:347][470]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:377][471]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:403][472]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:430][473]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:455][474]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:480][475]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:508][476]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:538][477]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:565][478]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:596][479]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:620][480]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:646][481]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:672][482]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:697][483]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:722][484]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:749][485]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:776][486]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:803][487]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:829][488]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:854][489]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:881][490]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:907][491]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:933][492]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:960][493]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.57:986][494]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:013][495]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:038][496]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:065][497]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:092][498]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:116][499]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:141][500]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:166][501]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:194][502]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:221][503]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:247][504]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:271][505]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:294][506]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:316][507]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:338][508]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:362][509]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:388][510]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:419][511]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:450][512]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:478][513]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:509][514]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:535][515]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:560][516]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:585][517]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:611][518]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:636][519]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:663][520]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:689][521]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:716][522]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:740][523]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:766][524]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:792][525]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:818][526]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:845][527]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:872][528]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:899][529]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:925][530]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:951][531]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.58:977][532]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:002][533]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:029][534]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:056][535]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:085][536]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:111][537]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:136][538]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:162][539]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:186][540]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:213][541]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:238][542]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:263][543]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:290][544]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:318][545]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:344][546]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:370][547]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:396][548]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:423][549]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:450][550]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:477][551]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:504][552]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:530][553]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:555][554]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:581][555]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:608][556]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:633][557]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:660][558]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:687][559]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:716][560]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:743][561]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:769][562]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:796][563]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:820][564]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:845][565]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:870][566]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:900][567]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:926][568]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:952][569]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.16.59:978][570]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:005][571]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:030][572]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:057][573]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:085][574]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:111][575]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:137][576]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:166][577]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:194][578]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:218][579]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:244][580]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:271][581]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:297][582]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:326][583]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:353][584]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:380][585]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:404][586]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:434][587]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:460][588]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:486][589]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:515][590]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:544][591]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:571][592]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:600][593]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:627][594]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:657][595]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:683][596]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:711][597]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:739][598]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:767][599]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:797][600]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:823][601]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:850][602]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:877][603]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:905][604]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.00:978][605]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:007][606]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:037][607]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:067][608]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:092][609]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:133][610]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:164][611]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:196][612]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:231][613]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:264][614]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:298][615]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:337][616]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:371][617]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:403][618]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:431][619]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:461][620]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:488][621]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:518][622]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:546][623]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:573][624]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:601][625]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:630][626]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:656][627]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:685][628]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:713][629]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:741][630]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:766][631]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:794][632]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:821][633]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:847][634]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:873][635]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:900][636]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:929][637]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:955][638]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.01:980][639]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.02:007][640]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.02:035][641]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.02:060][642]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.02:088][643]LogBlueprintUserMessages: [BP_BaoliCharacter_C_0] 10.0
[2025.06.14-08.17.02:145][643]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.14-08.17.02:145][643]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.14-08.17.02:147][643]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.14-08.17.02:147][643]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.14-08.17.02:147][643]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.14-08.17.02:150][643]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.14-08.17.02:154][643]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.14-08.17.02:161][643]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.14-08.17.02:182][643]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.14-08.17.02:206][643]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 2
[2025.06.14-08.17.02:207][643]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=2
[2025.06.14-08.17.02:209][643]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=2
[2025.06.14-08.17.02:213][643]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.14-08.17.02:218][643]LogUObjectHash: Compacting FUObjectHashTables data took   1.18ms
[2025.06.14-08.17.02:254][644]LogPlayLevel: Display: Destroying online subsystem :Context_8
[2025.06.14-08.17.37:131][682]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.06.14-08.18.25:786][161]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_3
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.25:786][161]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_3
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.29:553][340]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_4
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.29:553][340]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_4
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.29:667][341]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_5
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.29:667][341]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_5
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.29:696][342]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_6
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.29:696][342]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_6
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.29:746][343]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_7
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.29:746][343]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_7
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.31:281][390]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_8
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.31:282][390]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_8
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.31:647][400]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_9
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.31:647][400]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_9
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.31:674][401]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_10
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.31:675][401]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_10
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.31:715][402]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_11
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.31:715][402]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_11
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.31:744][403]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_12
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.31:744][403]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_12
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.31:773][404]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_13
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.31:773][404]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_13
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.31:802][405]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_14
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.31:802][405]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_14
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.31:832][406]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_15
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.31:832][406]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_15
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.31:861][407]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_16
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.31:861][407]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_16
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.31:889][408]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_17
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.31:889][408]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_17
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.31:920][409]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_18
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.31:921][409]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_18
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.31:952][410]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_19
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.31:952][410]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_19
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.31:981][411]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_20
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.31:981][411]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_20
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.32:011][412]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_21
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.32:011][412]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_21
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.18.35:548][547]LogAssetEditorSubsystem: Opening Asset editor for Blueprint /Game/Blueprints/BP_BaoliController.BP_BaoliController
[2025.06.14-08.18.35:548][547]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_7
[2025.06.14-08.18.35:587][547]LogTemp: Display: rdBPTools: Failed to load rdBPTools config ini file
[2025.06.14-08.18.35:587][547]LogTemp: Display: Handle AssetOpenedInEditor - BlueprintEditor...
[2025.06.14-08.18.35:597][547]LogStreaming: Display: FlushAsyncLoading(358): 1 QueuedPackages, 0 AsyncPackages
[2025.06.14-08.18.35:838][547]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.14-08.18.36:004][547]LogSourceControl: P4 execution time: 0.1656 seconds. Command: client -o super_dev
[2025.06.14-08.18.38:697][547]LogBlueprintEditor: Perf: 3.1 total seconds to load all 13 blueprint libraries in project. Avoid references to content in blueprint libraries to shorten this time.
[2025.06.14-08.18.38:697][547]LogBlueprintEditor: Perf: 3.0 seconds loading: /Game/UltraDynamicSky/Blueprints/Functions/UltraDynamicWeather_Functions
[2025.06.14-08.18.39:814][547]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.14-08.18.39:814][547]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.14-08.18.39:814][547]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/14 13:44:49
[2025.06.14-08.18.39:814][547]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.14-08.18.39:814][547]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.14-08.18.39:814][547]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.14-08.18.39:814][547]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.14-08.18.39:814][547]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.14-08.18.39:814][547]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.14-08.18.39:814][547]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.14-08.18.39:814][547]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.14-08.18.39:814][547]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.14-08.18.39:814][547]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.14-08.18.39:814][547]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.14-08.18.39:814][547]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.14-08.18.39:814][547]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.14-08.18.39:836][547]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.14-08.18.39:837][547]LogSlate: Took 0.000310 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-BoldCondensed.ttf' (158K)
[2025.06.14-08.18.39:998][547]LogSourceControl: P4 execution time: 0.1615 seconds. Command: client -o super_dev
[2025.06.14-08.18.40:294][548]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.14-08.18.40:294][548]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.14-08.18.40:294][548]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/14 13:44:49
[2025.06.14-08.18.40:294][548]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.14-08.18.40:294][548]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.14-08.18.40:294][548]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.14-08.18.40:294][548]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.14-08.18.40:294][548]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.14-08.18.40:294][548]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.14-08.18.40:295][548]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.14-08.18.40:295][548]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.14-08.18.40:295][548]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.14-08.18.40:295][548]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.14-08.18.40:295][548]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.14-08.18.40:295][548]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.14-08.18.40:295][548]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.14-08.18.40:464][559]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.14-08.18.40:465][559]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.14-08.18.40:555][566]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.14-08.18.40:632][573]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.14-08.18.40:710][580]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.14-08.18.40:789][587]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.14-08.18.47:735][191]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliController.uasset'
[2025.06.14-08.18.47:896][210]LogSourceControl: P4 execution time: 0.1614 seconds. Command: fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliController.uasset
[2025.06.14-08.18.48:692][304]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliController.uasset'
[2025.06.14-08.18.48:853][323]LogSourceControl: P4 execution time: 0.1619 seconds. Command: fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliController.uasset
[2025.06.14-08.18.56:333][ 19]LogAssetEditorSubsystem: Opening Asset editor for Blueprint /Game/Blueprints/BP_BaoliCharacter.BP_BaoliCharacter
[2025.06.14-08.18.56:333][ 19]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_8
[2025.06.14-08.18.56:349][ 19]LogConfig: Branch 'rdBPToolsConfig' had been unloaded. Reloading on-demand took 0.60ms
[2025.06.14-08.18.56:349][ 19]LogTemp: Display: Handle AssetOpenedInEditor - BlueprintEditor...
[2025.06.14-08.18.56:589][ 19]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.14-08.18.56:667][ 19]LogSourceControl: Attempting 'p4 client -o super_dev'
[2025.06.14-08.18.56:752][ 19]LogSourceControl: P4 execution time: 0.1627 seconds. Command: client -o super_dev
[2025.06.14-08.18.56:843][ 19]LogSourceControl: P4 execution time: 0.1764 seconds. Command: client -o super_dev
[2025.06.14-08.18.56:901][ 19]LogStreaming: Display: FlushAsyncLoading(556): 1 QueuedPackages, 0 AsyncPackages
[2025.06.14-08.18.57:044][ 20]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_9
[2025.06.14-08.18.57:056][ 20]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.14-08.18.57:056][ 20]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.14-08.18.57:056][ 20]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/14 13:44:49
[2025.06.14-08.18.57:056][ 20]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.14-08.18.57:056][ 20]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.14-08.18.57:056][ 20]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.14-08.18.57:056][ 20]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.14-08.18.57:056][ 20]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.14-08.18.57:056][ 20]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.14-08.18.57:056][ 20]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.14-08.18.57:056][ 20]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.14-08.18.57:056][ 20]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.14-08.18.57:056][ 20]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.14-08.18.57:056][ 20]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.14-08.18.57:056][ 20]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.14-08.18.57:056][ 20]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.14-08.18.57:116][ 21]SourceControl: CommandMessage Command: Connect, Info: Client super_dev
[2025.06.14-08.18.57:116][ 21]SourceControl: CommandMessage Command: Connect, Info: Update 2025/05/18 16:17:07
[2025.06.14-08.18.57:116][ 21]SourceControl: CommandMessage Command: Connect, Info: Access 2025/06/14 13:44:49
[2025.06.14-08.18.57:116][ 21]SourceControl: CommandMessage Command: Connect, Info: Owner super
[2025.06.14-08.18.57:116][ 21]SourceControl: CommandMessage Command: Connect, Info: Host DESKTOP-E41IK6R
[2025.06.14-08.18.57:116][ 21]SourceControl: CommandMessage Command: Connect, Info: Description Created by super.

[2025.06.14-08.18.57:116][ 21]SourceControl: CommandMessage Command: Connect, Info: Root H:\P4\dev
[2025.06.14-08.18.57:116][ 21]SourceControl: CommandMessage Command: Connect, Info: Options noallwrite noclobber nocompress unlocked nomodtime rmdir noaltsync
[2025.06.14-08.18.57:116][ 21]SourceControl: CommandMessage Command: Connect, Info: SubmitOptions revertunchanged
[2025.06.14-08.18.57:116][ 21]SourceControl: CommandMessage Command: Connect, Info: LineEnd local
[2025.06.14-08.18.57:116][ 21]SourceControl: CommandMessage Command: Connect, Info: Stream //baoli/pc/dev
[2025.06.14-08.18.57:116][ 21]SourceControl: CommandMessage Command: Connect, Info: View0 //baoli/pc/dev/... //super_dev/...
[2025.06.14-08.18.57:116][ 21]SourceControl: CommandMessage Command: Connect, Info: Type writeable
[2025.06.14-08.18.57:116][ 21]SourceControl: CommandMessage Command: Connect, Info: Backup enable
[2025.06.14-08.18.57:116][ 21]SourceControl: CommandMessage Command: Connect, Info: specFormatted 
[2025.06.14-08.18.57:116][ 21]SourceControl: CommandMessage Command: Connect, Info: func client-FstatInfo
[2025.06.14-08.18.57:146][ 22]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_10
[2025.06.14-08.18.57:169][ 23]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_11
[2025.06.14-08.18.57:336][ 39]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.14-08.18.57:410][ 46]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.14-08.18.57:484][ 53]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.14-08.18.57:573][ 60]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.14-08.18.57:650][ 67]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.14-08.18.58:996][202]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset'
[2025.06.14-08.18.59:162][222]LogSourceControl: P4 execution time: 0.1667 seconds. Command: fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset
[2025.06.14-08.18.59:997][313]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset'
[2025.06.14-08.19.00:157][313]LogSourceControl: P4 execution time: 0.1590 seconds. Command: fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset
[2025.06.14-08.19.00:334][313]LogUObjectHash: Compacting FUObjectHashTables data took   2.95ms
[2025.06.14-08.19.00:828][323]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.14-08.19.00:828][323]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.14-08.19.00:828][323]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.14-08.19.00:828][323]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.14-08.19.00:859][326]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.14-08.19.00:906][330]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.14-08.19.01:918][379]LogDebuggerCommands: Repeating last play command: Selected Viewport
[2025.06.14-08.19.01:951][379]LogPlayLevel: [PlayLevel] Compiling ABP_SandboxCharacter before play...
[2025.06.14-08.19.01:959][379]LogPlayLevel: [PlayLevel]   Compiling BP_BaoliController as a dependent...
[2025.06.14-08.19.02:107][379]LogScript: Warning: Attempted to access BP_BaoliCharacter_C_0 via property K2Node_DynamicCast_AsBP_Baoli_Character, but BP_BaoliCharacter_C_0 is not valid (pending kill or garbage)
	ABP_SandboxCharacter_C /Engine/Transient.World_4:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_1
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:SetReferences:0078
[2025.06.14-08.19.02:108][379]LogScript: Warning: Accessed None attempting to assign variable on an object
	ABP_SandboxCharacter_C /Engine/Transient.World_4:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_1
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:SetReferences:008D
[2025.06.14-08.19.02:108][379]LogScript: Warning: Attempted to access BP_BaoliCharacter_C_0 via property SandboxCharacter, but BP_BaoliCharacter_C_0 is not valid (pending kill or garbage)
	ABP_SandboxCharacter_C /Engine/Transient.World_4:PersistentLevel.BP_BaoliCharacter_C_0.CharacterMesh0.ABP_SandboxCharacter_C_1
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:SetReferences:00B9
[2025.06.14-08.19.02:294][379]LogUObjectHash: Compacting FUObjectHashTables data took   2.39ms
[2025.06.14-08.19.02:305][379]LogPlayLevel: PlayLevel: Blueprint regeneration took 368 ms (2 blueprints)
[2025.06.14-08.19.02:305][379]LogPlayLevel: Creating play world package: /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.14-08.19.02:365][379]LogPlayLevel: PIE: StaticDuplicateObject took: (0.059976s)
[2025.06.14-08.19.02:365][379]LogPlayLevel: PIE: Created PIE world by copying editor world from /Game/Levels/DefaultLevel.DefaultLevel to /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel (0.060014s)
[2025.06.14-08.19.02:396][379]LogUObjectHash: Compacting FUObjectHashTables data took   1.65ms
[2025.06.14-08.19.02:399][379]r.RayTracing.Culling = "0"
[2025.06.14-08.19.02:399][379]r.RayTracing.Shadows.EnableTwoSidedGeometry = "0"
[2025.06.14-08.19.02:399][379]LogChaosDD: Creating Chaos Debug Draw Scene for world DefaultLevel
[2025.06.14-08.19.02:401][379]LogPlayLevel: PIE: World Init took: (0.001549s)
[2025.06.14-08.19.02:402][379]LogAudio: Display: Creating Audio Device:                 Id: 3, Scope: Unique, Realtime: True
[2025.06.14-08.19.02:402][379]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.14-08.19.02:402][379]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.14-08.19.02:402][379]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.14-08.19.02:402][379]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.14-08.19.02:402][379]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.14-08.19.02:402][379]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.14-08.19.02:402][379]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.14-08.19.02:402][379]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.14-08.19.02:402][379]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.14-08.19.02:402][379]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.14-08.19.02:402][379]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.14-08.19.02:403][379]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.14-08.19.02:441][379]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter AUX Input (VB-Audio Voicemeeter VAIO)
[2025.06.14-08.19.02:441][379]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.14-08.19.02:441][379]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.14-08.19.02:441][379]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.14-08.19.02:442][379]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=3
[2025.06.14-08.19.02:442][379]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=3
[2025.06.14-08.19.02:445][379]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=3
[2025.06.14-08.19.02:445][379]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=3
[2025.06.14-08.19.02:445][379]LogInit: FAudioDevice initialized with ID 3.
[2025.06.14-08.19.02:445][379]LogAudio: Display: Audio Device (ID: 3) registered with world 'DefaultLevel'.
[2025.06.14-08.19.02:445][379]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 3
[2025.06.14-08.19.02:445][379]LogEnhancedInput: Enhanced Input local player subsystem has initalized the user settings!
[2025.06.14-08.19.02:447][379]LogLoad: Game class is 'GM_Sandbox_C'
[2025.06.14-08.19.02:474][379]LogSkeletalMesh: USkeletalMeshComponent: Recreating Clothing Actors for 'Legs' with 'm_med_nrw_btm_jeans_nrm_High'
[2025.06.14-08.19.02:482][379]LogWorld: Bringing World /Game/Levels/UEDPIE_0_DefaultLevel.DefaultLevel up for play (max tick rate 0) at 2025.06.14-13.49.02
[2025.06.14-08.19.02:489][379]LogWorld: Bringing up level for play took: 0.040596
[2025.06.14-08.19.02:492][379]LogOnline: OSS: Created online subsystem instance for: :Context_14
[2025.06.14-08.19.02:493][379]LogAnimation: Warning: SetBlendSpace called on an invalid context or with an invalid type
[2025.06.14-08.19.02:506][379]LogBlueprintUserMessages: [BP_BedDrawer_C_1] UpperDrawer1
[2025.06.14-08.19.02:506][379]LogBlueprintUserMessages: [BP_BedDrawer_C_1] LowerDrawer2
[2025.06.14-08.19.02:506][379]LogBlueprintUserMessages: [BP_Commode_2_C_1] LL_Drawer1
[2025.06.14-08.19.02:506][379]LogBlueprintUserMessages: [BP_Commode_2_C_1] LM_Drawer2
[2025.06.14-08.19.02:506][379]LogBlueprintUserMessages: [BP_Commode_2_C_1] LU_Drawer3
[2025.06.14-08.19.02:506][379]LogBlueprintUserMessages: [BP_Commode_2_C_1] RL_Drawer4
[2025.06.14-08.19.02:506][379]LogBlueprintUserMessages: [BP_Commode_2_C_1] RM_Drawer5
[2025.06.14-08.19.02:506][379]LogBlueprintUserMessages: [BP_Commode_2_C_1] RU_Drawer6
[2025.06.14-08.19.02:506][379]LogBlueprintUserMessages: [BP_Commode_C_1] MiddleBigDrawer1
[2025.06.14-08.19.02:506][379]LogBlueprintUserMessages: [BP_Commode_C_1] LowerBigDrawer2
[2025.06.14-08.19.02:506][379]LogBlueprintUserMessages: [BP_Commode_C_1] RightSmallDrawer3
[2025.06.14-08.19.02:506][379]LogBlueprintUserMessages: [BP_Commode_C_1] LeftSmallDrawer4
[2025.06.14-08.19.02:506][379]LogBlueprintUserMessages: [BP_Commode_C_1] UpperBigDrawer5
[2025.06.14-08.19.02:506][379]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Left1
[2025.06.14-08.19.02:506][379]LogBlueprintUserMessages: [BP_Cupboard_C_1] Cupboard_Right2
[2025.06.14-08.19.02:507][379]LogBlueprintUserMessages: [BP_Modern_Cupboard_C_1] SlidingDoor1
[2025.06.14-08.19.02:519][379]PIE: Server logged in
[2025.06.14-08.19.02:520][379]PIE: Play in editor total start time 0.596 seconds.
[2025.06.14-08.19.02:521][379]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.02:577][380]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.02:639][380]LogRenderer: Warning: [VSM] One Pass Projection max lights overflow. If you see shadow artifacts, decrease the amount of local lights per pixel, or increase r.Shadow.Virtual.OnePassProjection.MaxLightsPerPixel.
[2025.06.14-08.19.02:728][381]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.02:812][382]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.02:847][383]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.02:861][384]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.02:879][385]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.02:912][386]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.02:917][386]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.14-08.19.02:918][386]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.14-08.19.02:929][387]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.02:957][388]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.02:994][389]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:006][389]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.14-08.19.03:025][390]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:043][391]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:067][392]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:117][393]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:124][393]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.14-08.19.03:138][394]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:161][395]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:196][396]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:208][396]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.14-08.19.03:233][397]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:267][398]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:281][399]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:299][400]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:306][400]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.14-08.19.03:340][401]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:355][402]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:381][403]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:393][403]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.14-08.19.03:421][404]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:436][405]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:462][406]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:495][407]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:512][408]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:541][409]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:572][410]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:588][410]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 16777216 bytes
[2025.06.14-08.19.03:594][411]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:632][412]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:657][413]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:682][414]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:710][415]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:734][416]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:765][417]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:776][417]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.14-08.19.03:784][418]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:813][419]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:836][420]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:861][421]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:891][422]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:915][423]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:937][424]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:963][425]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.03:986][426]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:011][427]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:036][428]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:064][429]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:087][430]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:111][431]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:121][431]LogD3D12RHI: Display: Temp texture streaming buffer not large enough, needed 8388608 bytes
[2025.06.14-08.19.04:139][432]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:165][433]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:193][434]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:215][435]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:243][436]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:271][437]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:298][438]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:325][439]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:350][440]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:374][441]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:398][442]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:428][443]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:454][444]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:481][445]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:510][446]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:539][447]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:568][448]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:599][449]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:628][450]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:655][451]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:682][452]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:711][453]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:738][454]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:764][455]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:790][456]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:818][457]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:843][458]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:869][459]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:898][460]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:926][461]LogBlueprintUserMessages: [BP_BaoliController_C_0] false
[2025.06.14-08.19.04:954][462]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.04:981][463]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:008][464]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:034][465]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:060][466]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:088][467]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:116][468]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:146][469]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:172][470]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:200][471]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:225][472]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:251][473]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:278][474]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:304][475]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:331][476]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:359][477]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:389][478]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:416][479]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:443][480]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:469][481]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:496][482]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:522][483]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:550][484]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:578][485]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:604][486]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:631][487]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:656][488]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:683][489]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:709][490]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:735][491]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:761][492]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:789][493]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:818][494]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:846][495]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:871][496]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:899][497]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:925][498]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:953][499]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.05:980][500]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:007][501]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:037][502]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:064][503]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:091][504]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:117][505]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:143][506]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:168][507]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:196][508]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:224][509]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:253][510]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:280][511]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:306][512]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:333][513]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:360][514]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:386][515]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:414][516]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:442][517]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:469][518]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:502][519]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:536][520]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:567][521]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:595][522]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:620][523]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:648][524]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:678][525]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:707][526]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:733][527]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:759][528]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:785][529]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:811][530]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:839][531]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:868][532]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:901][533]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:930][534]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:962][535]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.06:994][536]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:024][537]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:049][538]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:077][539]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:106][540]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:134][541]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:161][542]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:187][543]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:213][544]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:240][545]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:269][546]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:295][547]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:324][548]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:350][549]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:376][550]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:402][551]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:428][552]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:454][553]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:480][554]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:510][555]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:541][556]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:566][557]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:594][558]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:619][559]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:645][560]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:670][561]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:698][562]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:725][563]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:753][564]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:780][565]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:807][566]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:832][567]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:859][568]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:884][569]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:912][570]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:940][571]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:969][572]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.07:997][573]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:023][574]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:050][575]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:076][576]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:102][577]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:132][578]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:157][579]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:185][580]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:212][581]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:240][582]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:267][583]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:292][584]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:319][585]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:347][586]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:376][587]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:405][588]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:431][589]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:459][590]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:485][591]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:515][592]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:542][593]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:569][594]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:597][595]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:625][596]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:652][597]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:678][598]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:703][599]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:728][600]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:755][601]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:783][602]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:811][603]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:840][604]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:868][605]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:894][606]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:920][607]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:947][608]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.08:974][609]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:000][610]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:029][611]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:055][612]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:082][613]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:111][614]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:138][615]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:163][616]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:190][617]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:217][618]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:245][619]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:271][620]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:299][621]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:327][622]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:352][623]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:380][624]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:407][625]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:432][626]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:460][627]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:489][628]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:515][629]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:543][630]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:568][631]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:594][632]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:620][633]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:647][634]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:674][635]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:701][636]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:729][637]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:755][638]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:782][639]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:809][640]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:836][641]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:861][642]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:889][643]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:954][644]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.09:990][645]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:017][646]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:044][647]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:069][648]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:096][649]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:125][650]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:152][651]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:177][652]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:204][653]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:232][654]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:258][655]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:287][656]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:315][657]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:342][658]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:369][659]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:395][660]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:422][661]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:448][662]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:475][663]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:502][664]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:531][665]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:560][666]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:587][667]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:615][668]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:641][669]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:667][670]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:694][671]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:721][672]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:747][673]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:776][674]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:804][675]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:832][676]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:860][677]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:885][678]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:912][679]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:940][680]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.10:970][681]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:000][682]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:027][683]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:055][684]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:081][685]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:107][686]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:136][687]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:164][688]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:192][689]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:220][690]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:249][691]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:276][692]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:303][693]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:331][694]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:357][695]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:385][696]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:416][697]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:444][698]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:472][699]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:501][700]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:531][701]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:559][702]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:587][703]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:616][704]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:646][705]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:674][706]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:702][707]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:729][708]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:757][709]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:783][710]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:808][711]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:837][712]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:865][713]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:897][714]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:926][715]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:954][716]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.11:982][717]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:009][718]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:036][719]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:065][720]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:093][721]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:140][722]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:186][723]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:212][724]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:240][725]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:266][726]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:294][727]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:336][728]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:371][729]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:400][730]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:429][731]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:457][732]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:493][733]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:527][734]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:554][735]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:582][736]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:609][737]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:634][738]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:661][739]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:687][740]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:712][741]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:738][742]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:766][743]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:795][744]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:823][745]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:849][746]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:876][747]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:902][748]LogBlueprintUserMessages: [BP_BaoliController_C_0] true
[2025.06.14-08.19.12:945][748]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.14-08.19.12:945][748]LogWorld: BeginTearingDown for /Game/Levels/UEDPIE_0_DefaultLevel
[2025.06.14-08.19.12:947][748]LogWebBrowser: Closing browser during destruction, this may cause a later crash.
[2025.06.14-08.19.12:947][748]LogWebBrowser: Deleting browser for Url=file:///H:/P4/dev/Baoli/Content//HTML/Home.html.
[2025.06.14-08.19.12:948][748]LogWorld: UWorld::CleanupWorld for DefaultLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.14-08.19.12:950][748]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.14-08.19.12:954][748]LogPlayLevel: Display: Shutting down PIE online subsystems
[2025.06.14-08.19.12:966][748]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.14-08.19.13:011][748]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.14-08.19.13:023][748]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID 3
[2025.06.14-08.19.13:024][748]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=3
[2025.06.14-08.19.13:026][748]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=3
[2025.06.14-08.19.13:036][748]LogUObjectHash: Compacting FUObjectHashTables data took   1.88ms
[2025.06.14-08.19.13:078][749]LogPlayLevel: Display: Destroying online subsystem :Context_14
[2025.06.14-08.19.14:135][788]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_22
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.19.14:135][788]LogScript: Warning: Accessed None trying to read property SandboxCharacter
	ABP_SandboxCharacter_C /Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0.ABP_SandboxCharacter_C_22
	Function /Game/Blueprints/ABP_SandboxCharacter.ABP_SandboxCharacter_C:ExecuteUbergraph_ABP_SandboxCharacter:1190
[2025.06.14-08.22.08:033][328]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 386.195374
[2025.06.14-08.22.09:033][331]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.14-08.22.09:033][331]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 386.862030, Update Interval: 353.329254
[2025.06.14-08.23.49:357][632]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliController.uasset'
[2025.06.14-08.23.49:517][633]LogSourceControl: P4 execution time: 0.1605 seconds. Command: fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliController.uasset
[2025.06.14-08.24.00:947][667]LogSourceControl: Attempting 'p4 fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset'
[2025.06.14-08.24.01:107][668]LogSourceControl: P4 execution time: 0.1596 seconds. Command: fstat -Or H:/P4/dev/Baoli/Content/Blueprints/BP_BaoliCharacter.uasset
