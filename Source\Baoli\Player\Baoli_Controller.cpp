// Fill out your copyright notice in the Description page of Project Settings.


#include "Baoli/Player/Baoli_Controller.h"

#include "GameFramework/PawnMovementComponent.h"
#include "Kismet/KismetMathLibrary.h"
#include "Kismet/KismetTextLibrary.h"
#include <EnhancedInputComponent.h>

static bool sbIsTriggered = false;
static FVector2D sPrevInputVector = FVector2D::ZeroVector;

namespace Utils
{
	static void SanitizeVector(FVector2D& Vector)
	{
		if (Vector.X == -0.0f)
		{
			Vector.X = 0.0f;
		}
		if (Vector.Y == -0.0f)
		{
			Vector.Y = 0.0f;
		}
	}
}

void ABaoli_Controller::BeginPlay()
{
	Super::BeginPlay();

	//Casting to Character
	Baoli_Character = Cast<ABaoli_Character>(UGameplayStatics::GetPlayerCharacter(GetWorld(), 0));

	//Getting Character Mesh
	Mesh = Baoli_Character->GetMesh();

	//Getting Camera Component;
	Cam = Baoli_Character->CameraComponent;

	SetupInput();

	//////////////////////////////////////////// Functions set by timer ////////////////////////////////////////////////

	auto getFuncString = [] (FName FuncName) -> FString
	{
		return UKismetTextLibrary::Conv_TextToString(UKismetTextLibrary::Conv_NameToText(FuncName));
	};

	//LineTraceFunction
	UKismetSystemLibrary::K2_SetTimer(this, getFuncString("LineTrace"), 0.01, true);

	//Update Camera
	UKismetSystemLibrary::K2_SetTimer(this, getFuncString("TraceforCamera"), 0.01, true);

	//Trace For Interaction
	UKismetSystemLibrary::K2_SetTimer(this, getFuncString("TraceforInteraction"), 0.05, true);

	//Ignore Line Trace Tag
	UGameplayStatics::GetAllActorsWithTag(GetWorld(), "IgnoreLineTrace", IgnoreLineTraceActors);
}

void ABaoli_Controller::Tick(float DeltaSeconds)
{
	Super::Tick(DeltaSeconds);
}

void ABaoli_Controller::LineTrace()
{
	// Use cached camera manager for better performance
	if (UGameplayStatics::GetPlayerCameraManager(GetWorld(), 0))
	{
		FVector Start = UGameplayStatics::GetPlayerCameraManager(GetWorld(), 0)->K2_GetActorLocation();
		FVector End = UGameplayStatics::GetPlayerCameraManager(GetWorld(), 0)->GetActorForwardVector() * TraceDistance + Start;
		bLineHit = UKismetSystemLibrary::SphereTraceSingle(GetWorld(), Start, End, 4.0f, ETraceTypeQuery::TraceTypeQuery1,
			true, IgnoreLineTraceActors, EDrawDebugTrace::None, hitResult, true);
	}
}

void ABaoli_Controller::SetupInput()
{
	Super::SetupInputComponent();

	// Cast to enhanced input component
	if (UEnhancedInputComponent* EnhancedInputComponent = Cast<UEnhancedInputComponent>(InputComponent))
	{
		// Bind actions with null checks
		if (MoveAction)
		{
			// Move Binding
			EnhancedInputComponent->BindAction(MoveAction, ETriggerEvent::Triggered, this, &ABaoli_Controller::MoveTriggeredInput);
			EnhancedInputComponent->BindActionValueLambda(MoveAction, ETriggerEvent::Completed, [](const FInputActionValue&) {sbIsTriggered = false;});
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("MoveAction is null in SetupInputComponent"));
		}
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("Enhanced Input Component is null in SetupInputComponent"));
	}
}

void ABaoli_Controller::MoveTriggeredInput(const FInputActionValue& Value)
{
	FVector2D MovementVector = Value.Get<FVector2D>();
	// Call the Blueprint implementable event
	// Note: If using BlueprintNativeEvent, use _Implementation suffix in C++
	// If using BlueprintImplementableEvent, you don't need to implement it in C++
	
	// NOTE: This will result in diagonal input...
	//if (sPrevInputVector != MovementVector)
	//	sbIsTriggered = false;

	//sPrevInputVector = MovementVector;

	switch (CharacterState)
	{
	case ECharacterState::OverWorld:
		//Add Movement Input
		Baoli_Character->Move(MovementVector);
		break;
	case ECharacterState::Mechanic:
		if (!sbIsTriggered)
		{
			Utils::SanitizeVector(MovementVector);
			MovementInput(MovementVector, MoveAction);
			sbIsTriggered = true;
		}
		break;
	case ECharacterState::GameMenu:
		if (!sbIsTriggered)
		{
			Utils::SanitizeVector(MovementVector);
			MovementInput(MovementVector, MoveAction);
			sbIsTriggered = true;
		}
		break;
	default:
		break;
	}
}

void ABaoli_Controller::TraceForInteraction()
{
	if(ShouldTrace())
	{
		if (bLineHit)
		{
			if (!bHoldTrace)
			{
				if (hitResult.Distance < 250.0f)
				{
					if (hitResult.GetComponent()->ComponentHasTag(FName("Interactable")))
					{
						FocusedObject = hitResult.GetActor();
						UE_LOG(LogTemp, Warning, TEXT("Focused Object = %s"), *FocusedObject->GetName())
						ActiveComponent = hitResult.GetComponent();
						ActiveComponent->SetRenderCustomDepth(true);
						bDrawWidget = true;

					}
					else
					{
						FocusedObject = nullptr;
						if (ActiveComponent != nullptr)
						{
							ActiveComponent->SetRenderCustomDepth(false);
							bDrawWidget = false;
						}
					}
				}
			}
			else
			{
				FocusedObject = nullptr;
				if (ActiveComponent != nullptr)
				{
					ActiveComponent->SetRenderCustomDepth(false);
					bDrawWidget = false;
				}
			}
		}
		else
		{
			bDrawWidget = false;
		}
	}
	else
	{
		FocusedObject = nullptr;
		if (ActiveComponent != nullptr)
		{
			ActiveComponent->SetRenderCustomDepth(false);
			bDrawWidget = false;
			ActiveComponent = nullptr;
		}

	}
}

void ABaoli_Controller::TraceForCamera()
{

	double TargetFocalDistance;
	if (bLineHit)
	{
		TargetFocalDistance = hitResult.Distance + 5;
		//UKismetSystemLibrary::PrintString(GetWorld(), FString("Line hit true and value set"));
	}
	else
	{
		TargetFocalDistance = 5000.0f;
		//UKismetSystemLibrary::PrintString(GetWorld(), FString("Line hit false and value set"));
	}
	SetFocalDistance = UKismetMathLibrary::FInterpTo(SetFocalDistance, TargetFocalDistance, UGameplayStatics::GetWorldDeltaSeconds(GetWorld()), 10.0f);
	Cam->PostProcessSettings.bOverride_DepthOfFieldFocalDistance = true;
	Cam->PostProcessSettings.DepthOfFieldFocalDistance = SetFocalDistance;

	if (Cam)
	{
		Cam->PostProcessSettings.bOverride_VignetteIntensity = true;
		if (bIsInspecting)
		{
			Cam->PostProcessSettings.VignetteIntensity = 1.0f;
		}
		else
		{
			Cam->PostProcessSettings.VignetteIntensity = 0.4f;
		}
	}

}

bool ABaoli_Controller::ShouldTrace()
{
	bool retValue;
	if (Baoli_Character->GetVelocity().Length() > 0.0f || Baoli_Character->GetMovementComponent()->IsFalling())
	{
		retValue =  false;
	}
	else
	{
		retValue = true;
	}

	return retValue;
}

void ABaoli_Controller::ChangeCharacterState(ECharacterState pCharacterState)
{
	CharacterState = pCharacterState;
}
